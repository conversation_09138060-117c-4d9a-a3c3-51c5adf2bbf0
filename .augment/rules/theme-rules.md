---
type: "agent_requested"
description: "Example description"
---

# Theme System Rules

## 1. Theme Access Patterns
✅ ALWAYS use `context.theme` for basic theme colors (primaryColor, cardColor, scaffoldBackgroundColor, dividerColor)
✅ ALWAYS use `context.colorTheme` for custom app colors (textPrimary, textTitle, textRegular, stockRed, stockGreen, etc.)
✅ ALWAYS use `context.textTheme` for text styles with extensions (primary, title, regular, secondary, tertiary, etc.)
❌ NEVER hardcode colors like `Color(0xFF525A79)` - use theme colors instead
❌ NEVER hardcode font families like 'PingFang SC' - let theme handle fonts

## 2. Color Usage Rules
```dart
// ✅ CORRECT - Theme colors
backgroundColor: context.theme.cardColor,
textColor: context.colorTheme.textPrimary,
primaryColor: context.theme.primaryColor,
stockGreen: context.colorTheme.stockGreen,

// ❌ WRONG - Hardcoded colors (only for exceptional case)
backgroundColor: Color(0xFFFFFFFF),
textColor: Color(0xFF525A79),
```

## 3. Text Styling Rules
❌ No need to use again the default styling like `.fs14` and `.w400`

```dart
// ✅ CORRECT - Use theme with extensions
Text(
  'title'.tr(),
  style: context.textTheme.primary.fs16.w600,
)

// ❌ WRONG - Hardcoded styles
Text(
  'title',
  style: TextStyle(
    fontFamily: 'PingFang SC',
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: Color(0xFF525A79),
  ),
)
style: context.textTheme.primary.fs14.w400,

```

## 4. Multi-Flavor Theme Integration
✅ Use template-specific widgets in `lib/shared/widgets/templates/`
✅ Check `context.appTheme.skinStyle` for runtime template detection
✅ Implement responsive layouts for different templates
✅ Use conditional rendering based on `AppConfig.instance.currentTradingModel`
✅ Follow existing patterns in home and profile screens
❌ Don't create template-specific duplicates unnecessarily
❌ Don't break template consistency within same flavor