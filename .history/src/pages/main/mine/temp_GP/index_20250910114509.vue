<template>
    <van-pull-refresh
        class="h-full overflow-auto p-4"
        :disabled="!$isLogin"
        :model-value="loading"
        @refresh="_onRefresh"
        v-bind:style="backgroundStyle"
    >
        <div class="h-full overflow-y-scroll rounded-lg ">
            <!-- 基础信息 -->
            <div data-aos="fade-left">
                <div v-if="!$isLogin" class="flex">
                    <div class="w-3/5 ml-auto flex gap-2.5">
                        <van-button
                            round
                            block
                            size="small"
                            type="primary"
                            to="/auth/register"
                        >
                            {{ t('auth.register') }}
                        </van-button>

                        <van-button
                            round
                            block
                            size="small"
                            type="primary"
                            to="/auth"
                        >
                            {{ t('auth.login') }}
                        </van-button>
                    </div>
                </div>

                <div v-else class="flex-middle gap-2.5 text-sm">
                    <c-avatar
                        :avatar="$profile.avatar"
                        @click="$router.push('/avatar')"
                    />
                    <div :class="[$theme === 'light' ? 'text-text': 'text-title']" @click="$router.push('/profile')">
                        <div class="normal-case flex-middle gap-2.5">
                            <span>{{ $profile.nickname }}</span>
                            <img
                                v-if="$profile.auth"
                                :src="verifyIcon[locale]"
                                alt="verify"
                                class="h-4"
                            >
                        </div>
                        <div>{{ $profile.mobile }}</div>
                        <div class="normal-case">uid: {{ $profile.uid }}</div>
                    </div>

                    <c-icon
                        class="ml-auto"
                        prefix="mine"
                        size="40"
                        :name="`vip_${$profile.level}`"
                        @click="$router.push('/vip')"
                    />
                </div>
            </div>
            <!-- 基础信息 -->

            <!-- 资产 -->
            <c-card
                class="my-4"
                data-aos-delay="50"
            >
                <div class="text-title text-sm mb-2.5 grid grid-cols-[1fr_1fr_auto] gap-2.5 text-center">
                    <div>
                        <div>{{ t('account.balance') }}</div>
                        <c-rate-currency
                            :amount="$spot.usableCash"
                            :disabled="!$isLogin"
                            v-model:currency="currency"
                            v-model:rate="rate"
                        />
                    </div>
                    <div @click="handleInterest">
                        <div>
                            {{ t('account.interest') }}
                            <!--<van-icon name="arrow"/>-->
                            <van-icon name="play" class="ml-[-5px]"/>
                        </div>
                        <c-amount :amount="$spot.interestCash * rate"/>
                    </div>
                    <van-space>
                        <van-button
                            type="primary"
                            size="mini"
                            @click="$router.push('/spot/financial/true')"
                        >
                            {{ t('_fund') }}
                        </van-button>
                    </van-space>
                </div>

                <div class="bg-controller_bg rounded-lg h-10 flex-between px-4">
                    <span class="text-sm">{{ t('account.freeze') }}</span>
                    <c-amount :amount="$spot.freezeCash * rate"/>
                </div>
            </c-card>
            <!-- 资产 -->

            <c-card
                data-aos-delay="150"
                class="flex-middle mb-4"
                v-if="$globalConfig.service"
            >
                <img
                    src="../assets/service.png"
                    alt="service"
                    class="w-16"
                >

                <div class="flex-1 ml-5">
                    <div class="mr-auto text-title">
                        {{ t('_exclusive') }}
                    </div>

                    <van-button
                        type="primary"
                        size="mini"
                        @click="handleToService"
                    >
                        {{ t('_service') }}
                    </van-button>
                </div>
            </c-card>

            <!-- 快捷导航 -->
            <c-card data-aos-delay="200">
                <van-grid
                    :border="false"
                    clickable
                >
                    <van-grid-item
                        data-aos="zoom-in"
                        v-for="({ title, icon, to, handler }, i) in nav"
                        :data-aos-delay="i * 50 + 50"
                        :key="icon"
                        :text="title"
                        :to
                        @click="handler"
                    >
                        <template #icon>
                            <c-icon
                                prefix="mine"
                                size="30"
                                theme
                                :name="icon"
                            />
                        </template>
                    </van-grid-item>
                </van-grid>
            </c-card>
            <!-- 快捷导航 -->
        </div>
    </van-pull-refresh>

    <FundTransfer v-model="showFundTransfer" @close="closeFundTransfer" />
    <CouponDialog
        v-model:show="showCouponModal"
        title="利息券"
        :from-type="1"
        :page-size="20"
    />
</template>

<script setup>
import { useMine } from '@/pages/main/mine/composables/index.js'
import bgImage1 from '/skins/templates/_TEMPLATE_/_THEME_/watermark.png'
import bgImage2 from '/skins/templates/_TEMPLATE_/_THEME_/bg.png'

const {
    router,
    verifyIcon,
    $isLogin,
    $profile,
    $spot,
    $globalConfig,
    handleInterest,
    onDownload,
    currency,
    rate,
    loading,
    _onRefresh,
    onLogoutConfirm,
    handleToService,
    t,
    locale,
    $theme,
    FundTransfer,
    showFundTransfer,
    handleFundTransfer,
    closeFundTransfer,
    CouponDialog,
    showCouponModal
} = useMine()

const nav = computed(() => [ {
    title: 'VIP', icon: 'vip', to: '/vip',
}, {
    title: t('header.mission'), icon: 'mission', to: '/mission',
}, {
    title: t('_financial'), icon: 'financial', handler: () => {
        handleFundTransfer()
    },
}, {
    title: t('header.third'), icon: 'third', handler: () => {
        sessionStorage.setItem('depositActiveTab', 'third')
        router.push('/deposit/third')
    },
}, {
    title: t('_invite'), icon: 'invite', to: '/invite',
}, {
    title: t('header.q&a'), icon: 'question', to: '/q&a',
}, {
    title: t('header.authentication'), icon: 'authentication', to: '/authentication',
}, {
    title: t('header.setting'), icon: 'setting', to: '/setting',
}, {
    title: t('header.about'), icon: 'information', to: '/about',
},  {
    title: t('_exchange'), icon: 'exchange', to: '/exchange',
}, {
    title: t('auth.logout'), icon: 'logout', handler: onLogoutConfirm,
} ])

if ($globalConfig.value?.app_download_enabled) {
    const item = {
        title: t('_download'),
        icon: 'download',
        handler: onDownload,
    };

    // 计算倒数第三的位置
    const position = Math.max(nav.value.length - 2, 0);
    nav.value.splice(position, 0, item);
}

const backgroundStyle = computed(() => {
    return {
        backgroundImage: `url("${bgImage1}"), url("${bgImage2}")`,
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'right 20px, top',
        backgroundSize: '150px, contain',
        overflow: 'hidden',
    }
})

defineOptions({ name: 'profile' })
</script>

<style scoped>
.van-grid {
    --van-grid-item-text-color: var(--title);
    --van-grid-item-content-padding: 8px;
}
</style>
