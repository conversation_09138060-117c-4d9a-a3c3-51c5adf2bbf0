import _ from 'lodash'

import i18n, { onChangeLanguage } from '@/i18n/index.js'
import socket from '@/socket.js'
import Protocol from '@/components/Protocol/index.vue'
import { utils_get_os, utils_link2 } from '@/utils/index.js'

export const useSocket = (type, callback) => {
    const emit = async (params) => {
        await socket.emit({
            type,
            action: SOCKET_ACTIONS.SUBSCRIBE,
            params,
        })
    }

    socket.on(type, ({ data }) => {
        callback(data)
    })

    const off = async () => {
        await socket.emit({
            type,
            action: SOCKET_ACTIONS.DELSUBSCRIBE,
        })
    }

    tryOnBeforeUnmount(off)

    return {
        emit,
        off,
    }
}

/**
 * @function useCssVariable
 * @description 用于获取css变量
 * @param names {string[]} 需要取的变量名
 * */
export const useCssVariable = (names) => {
    const root = getComputedStyle(document.documentElement)

    return names?.map(e => root.getPropertyValue(`--${e}`))
}

export const SETTING_TYPES = {
    LANGUAGE: 'translation',
    THEME: 'theme',
    RAISE_FALL_COLOR: 'raise_fall_color',
    CURRENCY: 'currency',
}
/**
 * @function useSystemSetting
 * @description 系统配置
 * */
export const useSystemSetting = (defaultAction) => {
    const { t, locale } = useI18n({
        useScope: 'global',
        messages: {
            [LANGUAGE.zhCN]: {
                _setting: {
                    light: '浅色主题',
                    dark: '深色主题',
                    red_green_trend: '红涨绿跌',
                    green_red_trend: '绿涨红跌',
                    language: '语言',
                    currency_default: '默认币种',
                    raise_fall_color: '涨跌颜色',
                    theme: '主题',
                },
            },
            [LANGUAGE.zhHK]: {
                _setting: {
                    light: '淺色主题',
                    dark: '深色主题',
                    red_green_trend: '紅漲綠跌',
                    green_red_trend: '綠漲紅跌',
                    language: '语言',
                    currency_default: '默认币种',
                    raise_fall_color: '涨跌颜色',
                    theme: '主题',
                },
            },
            [LANGUAGE.enUS]: {
                _setting: {
                    light: 'light',
                    dark: 'dark',
                    red_green_trend: 'red up, green down',
                    green_red_trend: 'green up, red down',
                    language: 'language',
                    currency_default: 'default currency',
                    raise_fall_color: 'raise fall color',
                    theme: 'theme',
                },
            },
        },
    })

    // 系统主题
    const isDark = useDark(),
        toggleDark = useToggle(isDark)

    const config = computed(() => [ // 语言
        {
            title: t('_setting.language'),
            value: LOCALE_CONFIG[locale.value],
            key: SETTING_TYPES.LANGUAGE,
        }, // 主题
        {
            title: t('_setting.theme'),
            value: t(`_setting.${$theme.value}`),
            key: SETTING_TYPES.THEME,
        }, // 涨跌颜色
        {
            title: t('_setting.raise_fall_color'),
            value: t(`_setting.${$raise_fall_color.value}`),
            key: SETTING_TYPES.RAISE_FALL_COLOR,
        },
    ])

    // 选项类型
    const actionType = ref(defaultAction)

    // 语言选项配置
    const langActions = computed(() => _.entries(LOCALE_CONFIG).map(e => {
        const [ key, title ] = e
        return {
            title,
            key,
            active: locale.value === key,
        }
    }))

    // 主题选项配置
    const themeActions = computed(() => _.values(THEME_CONFIG).map(e => {
        return {
            title: t(`_setting.${e}`),
            key: e,
            active: $theme.value === e,
        }
    }))

    // 涨跌颜色选项配置
    const raiseFallColorActions = computed(() => _.values(RAISE_FALL_COLOR_CONFIG).map(e => {
        return {
            title: t(`_setting.${e}`),
            key: e,
            active: $raise_fall_color.value === e,
        }
    }))

    const optionConfig = computed(() => {
        switch (actionType.value) {
            case SETTING_TYPES.LANGUAGE:
                return {
                    actions: unref(langActions),
                    onSelect: onChangeLanguage,
                }
            case SETTING_TYPES.THEME:
                return {
                    actions: unref(themeActions),
                    onSelect: (newTheme) => {
                        if ($theme.value !== newTheme) {
                            toggleDark()
                            $theme.value = newTheme
                        }
                    },
                }
            case SETTING_TYPES.RAISE_FALL_COLOR:
                return {
                    actions: unref(raiseFallColorActions),
                    onSelect: (newColor) => {
                        if ($raise_fall_color.value !== newColor) {
                            document.documentElement.className = document.documentElement.className.replace($raise_fall_color.value, newColor)
                            $raise_fall_color.value = newColor
                        }
                    },
                }
            default:
                return []
        }
    })

    return {
        config,

        actionType,
        optionConfig,

        langActions,
        themeActions,
        raiseFallColorActions,
    }
}

export const FILE_TYPES = {
    IMAGE: 'IM_IMAGE',
    VIDEO: 'IM_VIDEO',
    AUDIO: 'IM_AUDIO',
}
/**
 * @function useUpload
 * @description 上传资源
 * */
export const useUpload = (params) => {
    const { fileType = FILE_TYPES.IMAGE, onSuccess, onError } = params ?? {}

    const { t } = useI18n({
        messages: {
            [LANGUAGE.zhCN]: {
                _上传成功: '上传成功！',
                _未获取到文件: '未获取到文件',
            },
            [LANGUAGE.zhHK]: {
                _上传成功: '上传成功！',
                _未获取到文件: '未获取到文件',
            },
            [LANGUAGE.enUS]: {
                _上传成功: 'Upload Successfully!',
                _未获取到文件: 'File not found',
            },
        },
    })

    const url = ref('')

    const [ onUpload, loading ] = useFetchLoading(async ({ file }) => {
        const params = new FormData()
        params.set('file', file)

        loading.value = true
        try {
            const res = await api_post({
                url: `/upload/${fileType}`,
                params,
            })

            url.value = res.url

            showSuccessToast(t('_上传成功'))
            onSuccess?.(res.url)
        } catch (e) {
            onError?.(e)
        } finally {
            loading.value = false
        }
    })

    const upload = () => new Promise((resolve, reject) => {
        const input = document.createElement('input')
        input.type = 'file'
        input.accept = 'image/*'

        input.onchange = async (event) => {
            const target = event.target

            if (target.files?.length) {
            } else {
                reject(t('_未获取到文件'))
            }
        }
        input.onerror = (event) => {
            reject(event)
        }
        input.click()
    })

    return {
        url,
        loading,
        onUpload,
    }
}

/**
 * @function useFetchLoading
 * @description 给异步函数导出 loading
 * @param fetch {(params: any, loadingToast: any) => Promise<void>} 请求函数
 * @param [option] {{ toast: boolean }} 配置项：toast 是否展示toast
 * @return {[ () => Promise<void>, boolean ]}
 * */
export const useFetchLoading = (fetch, option) => {
    const {
        toast = true,
} = option ?? {}

    const loading = ref(false)
    let activeFetchCount = 0

    const onFetch = async (...args) => {
        let loadingToast
        activeFetchCount++
        loading.value = true

        try {
            if (toast) {
                loadingToast = showLoadingToast({
                    duration: 2500, //
                    position: 'center',
                    // forbidClick: true,
                    message: i18n.global.t('common.loading'),
                })
            }

            // 超时保护（可选）
            return await Promise.race([
                fetch(...args),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Request timeout')), 20000) // 20 秒
                )
            ])
        } catch (e) {
            console.error('Fetch error:', e)
            throw e  // 抛出给外部 catch
        } finally {
            try {
                loadingToast?.close?.()
            } catch (err) {
                console.warn('Toast close error', err)
            }

            activeFetchCount--
            if (activeFetchCount <= 0) {
                activeFetchCount = 0  // 保险起见
                loading.value = false
            }
        }
    }

    return [onFetch, loading]
}

export const useDeleteConfirm = (confirmProps) => {
    const { t } = useI18n({
        messages: {
            [LANGUAGE.zhCN]: {
                _确认提示: '是否确认删除？',
            },
            [LANGUAGE.zhHK]: {
                _确认提示: '是否确认删除？',
            },
            [LANGUAGE.enUS]: {
                _确认提示: 'Confirm Delete ?',
            },
        },
    })

    const onDeleteConfirm = async () => {
        await showConfirmDialog({
            title: t('_确认提示'),
            ...confirmProps,
        })
    }

    return {
        onDeleteConfirm,
    }
}

/** 倒计时 */
    // 所有倒计时对象
const countdownSet = useLocalStorage('countdownSet', {})
// 倒计时定时器
let countdownInterval
// 清空定时器
const onClearInterval = () => {
    clearInterval(countdownInterval)
    countdownInterval = undefined
}
// 初始化倒计时
const onInitialCountdown = () => {
    onClearInterval()

    let keys = _.keys(countdownSet.value)
    // 判断是否有未完成的倒计时
    if (keys.length) {
        countdownInterval = setInterval(() => {
            // 每次检测最新的倒计时对象
            keys = _.keys(countdownSet.value)

            keys.forEach(e => {
                // 正常倒计时
                if (countdownSet.value[e]) {
                    countdownSet.value[e] = countdownSet.value[e] - 1
                } else {
                    // 倒计时结束删除对应的值
                    delete countdownSet.value[e]
                }
            })

            // 当所有倒计时都完成后清除定时器
            if (!keys.length) onClearInterval()
        }, 1000)
    }
}
// 页面加载时初始化
onInitialCountdown()

/**
 * @function _useCountdown
 * @description 倒计时功能函数
 * @return {{ countdown: Ref<>, start: () => void }}
 * */
export const _useCountdown = (name, config) => {
    const { duration = 60 } = config ?? {}

    const intervalKey = `${name}@countdown`

    const start = () => {
        if (!countdownSet.value[intervalKey]) {
            countdownSet.value[intervalKey] = duration
        }
        onInitialCountdown()
    }

    const countdown = computed(() => countdownSet.value[intervalKey])

    return {
        countdown,
        start,
    }
}
/** 倒计时 */

export const useCurrentLang = () => {
    const { locale } = useI18n({ useScope: 'global' })

    return computed(() => ({
        isCN: locale.value === LANGUAGE.zhCN,
        isHK: locale.value === LANGUAGE.zhHK,
        isEN: locale.value === LANGUAGE.enUS,
    }))
}

export const useProtocolPopup = (type) => {
    const popup = ref(false)

    const { $protocol } = storeToRefs(useProtocolStore())

    const protocol = computed(() => {
        return _.find($protocol.value, { type }) ?? {
                id: 0,
                title: '',
                content: '',
                sealImageUrl: ''
        }
    })

    const onShowProtocol = async () => {
        popup.value = true
    }

    const ProtocolPopup = (props) => <Protocol
        {...protocol.value}
				{...props}
        modelValue={popup.value}
        onUpdate:modelValue={val => (popup.value = val)}
    />

    return {
        popup,
        onShowProtocol,
        ProtocolPopup,
    }
}



export const useIntervalFetch = (fetchOption, {
    paginationOption,
    config,
}) => {
    const { delay = 5000, method, callback } = config ?? {}

    let intervalInstance

    const clearIntervalInstance = () => {
        clearInterval(intervalInstance)
        intervalInstance = null
    }

    // 在 useIntervalFetch 顶部定义
    const startInterval = (_res, _params) => {
        clearIntervalInstance()
        intervalInstance = setInterval(async () => {
            const status = callback ? await callback(_res, _params) : true

            if (status) {
                const params = isRequest ? _params : {
                    ..._params,
                    pageNumber: 1,
                    pageSize: (_params?.pageNumber || 1) * (_params?.pageSize || paginationOption?.pageSize || 20),
                }

                const fetchRes = await api_fetch({
                    method: FETCH_METHOD.GET,
                    ...fetchOption,
                    params,
                    options: {
                        headers: { cancellable: fetchOption.cancellable },
                        signal,
                    },
                })

                if (isRequest) {
                    request.res.value = fetchRes
                } else {
                    request.list.value = fetchRes?.[paginationOption?.responseKeys?.list ?? 'records']
                }
            }
        }, delay)
    }


    const isRequest = method === FETCH_INTERVAL_TYPE.REQUEST,
        fetch = isRequest ? useRequest : usePagination

    const controller = new AbortController(),
        signal = controller.signal

    const request = fetch({
        ...fetchOption,
        onSuccess: (_res, _params) => {
            startInterval(_res, _params)
        },
        onErr: clearIntervalInstance,
    }, paginationOption)

    tryOnBeforeUnmount(() => {
        if (controller && fetchOption?.cancellable !== false) controller.abort()
        clearIntervalInstance()
    })

    const onRefresh = async () => {
        clearIntervalInstance()
        const res = await request.onRefresh()
        startInterval(res, fetchOption.params) // ⭐ 重启 interval
    }

    const onLoadMore = async () => {
        clearIntervalInstance()
        const res = await request.onLoadMore()
        startInterval(res, fetchOption.params) // ⭐ 重启 interval
    }

    return [
        {
            ...request,
            onRefresh,
            onLoadMore,
        },
        {
            clearIntervalInstance,
        },
    ]
}

export const useCaptcha = () => {
    const validate = ref('')

    const onAwaitCaptcha = () => new Promise((resolve, reject) => {
        initNECaptcha(
            {
                captchaId: '7650f145f0824ba6973d99d43a99d15c',
                width: _.min([ document.body.clientWidth, 640 ]) * .8,
                mode: 'popup',
                apiVersion: 2,
                popupStyles: {
                    position: 'fixed',
                    top: '20%',
                },
                onVerify: (err, data) => {
                    if (!err) {
                        validate.value = data.validate
                        resolve(data)
                    }
                },
                onClose: () => {
                    const els = document.querySelectorAll('.yidun_popup--light.yidun_popup.yidun_popup--size-small.yidun_popup--append')
                    if (els?.length) {
                        els?.forEach(el => {
                            el.style.display = 'none'
                        })
                    }
                    reject()
                },
            },
            instance => {
                instance.verify()
            },
            err => reject(err),
        )
    })

    return {
        onAwaitCaptcha,
        validate,
    }
}

export const useDownload = () => {
    const {isIOS} = utils_get_os()
    const { $globalConfig } = storeToRefs(useGlobalStore())
    return () => {
        const { download_href,  ios_download_url } = $globalConfig.value
        utils_link2(isIOS ?  ios_download_url:  download_href)
    }
}
