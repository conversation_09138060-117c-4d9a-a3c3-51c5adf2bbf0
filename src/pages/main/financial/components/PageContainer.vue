<template>
    <c-header :title>
        <template #right>
            <van-icon
                v-if="to"
                name="todo-list-o"
                size="20"
                @click="push(to)"
            />
        </template>
    </c-header>

    <div class="with-header-container__noPadding px-2.5 pb-2.5">
        <van-tabs
            data-aos="fade-left"
            v-model:active="activeTab"
        >
            <van-tab
                v-for="{name, title, slotName} in tabs"
                class="pt-2.5"
                :name="name"
                :title="title"
                :key="slotName"
            >
                <slot :name="slotName"/>
            </van-tab>
        </van-tabs>

        <c-service />
    </div>
</template>

<script setup>
const route = useRoute()
const { type } = defineProps({
    type: {
        type: String,
        required: true,
    },
    title: {
        type: String,
        required: true,
    },
    to: {
        type: [ String, Object ],
    },
})

const { params: { tab } } = useRoute(),
    { push } = useRouter()

const tabs = computed(() => {
    const list = [
        {
            name: 'bank',
            title: t('_bank'),
            slotName: 'bank',
        },
        {
            name: 'third',
            title: t('_third'),
            slotName: 'third',
        },
        {
            name: 'USDT',
            title: 'USDT',
            slotName: 'usdt',
        },
        {
            name: 'aliPay',
            title: t('_aliPay'),
            slotName: 'aliPay',
        }
    ]
    if(route.path === '/withdrawal') {
        return list
    } else {
        return list.slice(0, 3)
    }
})

const activeTab = useSessionStorage(`${type}ActiveTab`, tab)

const { t } = useI18n({
    messages: {
        [LANGUAGE.zhCN]: {
            _bank: '银行卡',
            _third: '三方渠道',
            _aliPay: '支付宝'
        },
        [LANGUAGE.zhHK]: {
            _bank: '银行卡',
            _third: '三方渠道',
            _aliPay: '支付寶'
        },
        [LANGUAGE.enUS]: {
            _bank: 'Bank',
            _third: 'Third Channel',
            _aliPay: 'Alipay'
        },
    },
})

defineOptions({ name: 'FinancialPageContainer' })
</script>

<style scoped>
</style>
