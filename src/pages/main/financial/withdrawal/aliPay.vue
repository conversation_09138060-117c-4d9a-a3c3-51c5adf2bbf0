<template>
    <!-- 触发区域 -->
    <c-card class="cursor-pointer select-none">
        <div class="flex items-center justify-between !text-sm">
            <span>提现账户</span>
            <div class="flex items-center justify-end" @click="showPayList = true">
                <span class="mr-1">13333333333</span>
                <c-icon name="arrow-right" size="14" />
            </div>
        </div>
    </c-card>

    <c-card class="cursor-pointer select-none mt-2.5" title="请输入提现金额(最低100元)">
        <div class="flex items-center justify-between !text-sm">
            <div class="flex flex-col w-full">
               <c-input class="w-full">
                   <template #prefix>
                       <span class="font-bold">￥</span>
                   </template>
                   <template #suffix>
                       <span @click="handleWithdrawalAll" type="link">
                           全部提现
                       </span>
                   </template>
               </c-input>
                <div class="text-sm mt-5">
                    {{ t('account.balance') }}
                    <c-amount :amount="$spot.usableCash"/>
                </div>
            </div>
        </div>
    </c-card>

    <c-submit class="mt-5">
        立即提现
    </c-submit>

    <!-- 底部弹框 -->
    <van-popup
        v-model:show="showPayList"
        position="bottom"
        teleport="body"
        round
        safe-area-inset-bottom
        close-on-popstate
        :style="{ borderRadius: '16px 16px 0 0' }"
    >
        <div class="max-h-[72vh] flex flex-col bg-white">
            <!-- 标题 -->
            <div class="flex items-center justify-center p-2.5 shrink-0 relative">
                <div class="text-slate-600 font-semibold text-[16px]">切换提现方式</div>
                <span
                    class="text-primary absolute top-2.5 right-6"
                    @click="handleManageClick"
                >
                    {{!managing ? '管理' : '保存'}}
                </span>
            </div>

            <!-- 表头 -->
            <div class="grid grid-cols-[1fr_1.3fr_45px] px-4 py-2 text-slate-400 text-sm! gap-x-2.5 shrink-0">
                <div class="text-left">名称</div>
                <div class="text-left">卡号</div>
                <div class="text-center">选择</div>
            </div>

            <!-- 中间区域：可滚动 -->
            <div class="flex-1 min-h-0 overflow-y-auto px-1 pb-2 space-y-1">
                <!-- 账户列表（单选） -->
                <van-radio-group v-model="current">
                    <div
                        v-for="item in list"
                        :key="item.id"
                        class="grid grid-cols-[1fr_1.3fr_45px] items-center mx-2.5 h-[45px]  border-t gap-x-2.5 border-slate-100 active:bg-slate-50"
                        @click="current = item.id"
                    >
                        <div class="text-slate-700">{{ item.name }}</div>
                        <div class="text-slate-700 tracking-wide">{{ item.number }}</div>
                        <div
                            class="flex justify-center"
                        >
                            <c-icon @click="handleDel" v-if="managing" name="delete" size="18" />
                            <van-radio @click="handleRowClick(item)" v-else :name="item.id" :label-disabled="true"/>
                        </div>
                    </div>
                </van-radio-group>

                <van-empty v-if="!list.length" description="暂无账户" class="py-8" />
            </div>

            <!-- 底部操作 -->
            <div
             class="h-11 shrink-0 flex-center gap-2 text-sm text-link border-t border-border"
             @click="handleShow"
            >
                <van-icon name="plus"/>
                <span>添加支付宝</span>
            </div>
        </div>
    </van-popup>
    <!--添加账户-->
    <AliPayAddModal v-model="showAddModal"/>
</template>

<script setup>
import { ref } from 'vue'
import AliPayAddModal from './aliPay.addModal.vue'
defineOptions({ name: 'withdrawal-aliPay' })

const showPayList = ref(true)
const showAddModal = ref(false)
const managing = ref(false)
const handleManageClick = () => {
    managing.value = !managing.value
}
const current = ref(null)
let uid = 2
const list = ref([
    { id: 1, name: '支付宝账户', number: '************' },
    { id: 2, name: '支付宝账户', number: '************' },
    { id: 3, name: '支付宝账户', number: '************' },
    { id: 4, name: '支付宝账户', number: '************' },
    { id: 5, name: '支付宝账户', number: '************' }
])

const handleWithdrawalAll = () => {

}

const handleRowClick = (item) => {
    showPayList.value = false
}
const handleDel = () => {
    // 删除添加的账户
}
const handleShow = () => {
    showAddModal.value = true
}
const { $spot } = storeToRefs(useAccountStore())

const {t} = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {

        },
        [LANGUAGE.zhHK]: {

        },
        [LANGUAGE.enUS]: {

        }
    }
})
</script>
