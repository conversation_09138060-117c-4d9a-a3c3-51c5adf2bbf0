<template>
    <c-header :title="t('address.addTitle')" />
   <div class="with-header-container">
       <c-card>
           <p class="text-title mb-2.5">{{t('address.withdraw')}}</p>
           <div class="w-full">
               <div class="flex items-center bg-white rounded-full">
                   <!-- 输入框 -->
                   <input
                       v-model="formState.walletAddress"
                       type="text"
                       :placeholder="t('address.placeholder')"
                       class="flex-1 bg-transparent outline-none text-gray-700 placeholder-gray-400"
                   />
                   <!-- 粘贴按钮 -->
                   <button
                       @click="handleCopyPaste"
                       class="ml-2 bg-primary text-white px-3 py-1 rounded-md text-sm  active:scale-95 transition"
                   >
                      {{t('address.paste')}}
                   </button>
               </div>
               <!-- 底部细线 -->
               <div class="border-b border-gray-200 mt-1"></div>
           </div>

           <p class="text-title my-2.5">{{t('address.withdraw')}}</p>
           <c-select
               :columns="networkTypes || []"
               :columns-field-names="{
                text: 'name',
                value: 'code'
            }"
               v-model:index="channelIndex"
           />
       </c-card>

         <c-card class="mt-4">
             <div class="w-full flex justify-between items-center">
                 <span>{{t('address.setDefault')}}</span>
                 <van-switch size="middle" v-model="formState.isWithdrawDefault" />
             </div>
         </c-card>

        <div @click="handleSubmit">
            <c-submit
                class="mt-10 mb-4"
                data-aos-delay="200"
                :loading
                :disabled="disabled"
            >
                {{t('address.add')}}
            </c-submit>
        </div>
   </div>
</template>

<script setup>
import { useNetworkTypesStore } from '@/store/usdt.js'

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            address: {
                addTitle: '添加USDT地址',
                withdraw: '提现地址',
                placeholder: '请输入提现地址',
                paste: '粘贴',
                setDefault: '设为默认USDT地址',
                add: '添加地址',
                clipboardError: '无法访问剪贴板，请手动输入'
            },
            operation: {
                successfully: '操作成功'
            }
        },
        [LANGUAGE.zhHK]: {
            address: {
                addTitle: '添加USDT地址',
                withdraw: '提現地址',
                placeholder: '請輸入提現地址',
                paste: '粘貼',
                setDefault: '設為默認USDT地址',
                add: '添加地址',
                clipboardError: '無法訪問剪貼板，請手動輸入'
            },
            operation: {
                successfully: '操作成功'
            }
        },
        [LANGUAGE.enUS]: {
            address: {
                addTitle: 'Add USDT Address',
                withdraw: 'Withdrawal Address',
                placeholder: 'Enter withdrawal address',
                paste: 'Paste',
                setDefault: 'Set as Default USDT Address',
                add: 'Add Address',
                clipboardError: 'Unable to access clipboard, please enter manually'
            },
            operation: {
                successfully: 'Operation Successful'
            }
        }
    }
})

const router = useRouter()
const loading = ref(false)
const channelIndex = ref(0)
const formState = reactive({
    walletAddress: '',
    isWithdrawDefault: false
})

const {networkTypes} = storeToRefs(useNetworkTypesStore())

const handleCopyPaste = async () => {
    try {
        formState.walletAddress = await navigator.clipboard.readText()
    } catch (err) {
        showFailToast(t('address.clipboardError'))
    }
}

const disabled = useFormDisabled(formState, [ 'isWithdrawDefault' ])

const handleSubmit = async () => {
    try {
        loading.value = true
        await api_post({
            url: '/user/wallet/add',
            params: {
                ...formState,
                network: networkTypes.value[channelIndex.value]?.code || '',
                isWithdrawDefault: formState.isWithdrawDefault ? 1 : 0
            },
        })
        showSuccessToast(t('operation.successfully'))
        router.back()
    }  finally {
        loading.value = false
    }
}
</script>