<template>
    <form @submit="onValid">
        <c-card
            data-aos-delay="100"
            :title="t('deposit.mainnet')"
        >
            <template #extra>
                <span class="text-sm">{{t('deposit.limit')}}:{{itemData?.minWithdrawalAmount}}-{{itemData?.maxWithdrawalAmount}}</span>
            </template>
            <c-select
                :columns="withdrawalList || []"
                :columns-field-names="{
                text: 'netWorkName',
                value: 'network'
            }"
                v-model:index="channelIndex"
            />
            <p class="text-xs mt-2.5">
               {{t('deposit.notice')}}
            </p>
        </c-card>

        <c-card
            class="mt-4"
            data-aos-delay="100"
            :title="t('deposit.address')"
        >
            <!-- 没有配置充值地址 -->
            <div v-if="!_walletList?.length"
                 class="flex items-center justify-center bg-controller_bg h-[77px] rounded-lg"
                 @click="$router.push('/usdt-address')"
            >
                <c-icon name="plus"/>
                <span class="ml-5">{{t('deposit.addAddress')}}</span>
            </div>


            <div v-else class="flex flex-col items-start justify-center bg-controller_bg h-[77px] rounded-lg pl-4 relative"
            >
                <span>{{defaultWallet?.netWorkName}}</span>
                <span class="text-xs mt-3">{{defaultWallet?.walletAddress}}</span>
                <span @click="showAddressSel = true" class="absolute right-0 top-0 flex items-center justify-center text-white text-xs bg-primary px-2 py-1 rounded-tr-lg rounded-bl-lg">
                    {{t('deposit.change')}}
                    <c-icon class="ml-0.25" name="arrow-right" size="12" />
                </span>
            </div>
        </c-card>

        <!--v-if="+siteId !== 83"-->
        <c-card
            data-aos-delay="200"
            :title="t('deposit.amount')"
            class="mt-4"
        >
            <template #extra>
                <span class="text-sm font-weight-400">{{t('deposit.amount')}}:1: {{itemData?.exchangeRate}}</span>
            </template>
            <!-- 倍数 -->
            <c-picker
                class="mb-2"
                :loading="loading"
                :options="amountOptions"
                v-model:index="data.amountIndex"
            />
            <!-- 倍数 -->
            <c-input
                data-aos="fade-left"
                data-aos-delay="50"
                :placeholder="t('deposit.inputPlaceholder')"
                type="number"
                inputmode="decimal"
                v-model="formState.amount"
            />

            <div class="text-sm mt-5">
                {{ t('account.balance') }}
                <c-amount :amount="$spot.usableCash"/>
            </div>
        </c-card>

        <c-submit
            class="mt-10 mb-4"
            data-aos-delay="200"
            :disabled
            :loading
        />
    </form>

    <!-- 切换提现地址 -->
    <van-popup teleport="body" destroy-on-close v-model:show="showAddressSel" position="bottom">
        <p class="flex justify-center items-center relative py-2.5">
            <span class="text-sm">{{t('deposit.addressSelect')}}</span>
            <van-button
                type="link"
                class="text-primary absolute right-0"
                @click="$router.push('/usdt-address')"
            >
                {{t('deposit.manage')}}
            </van-button>
        </p>
        <ul>
            <li v-for="(v) in _walletList">
                <div class="flex flex-col items-start justify-center bg-controller_bg h-[77px] rounded-lg px-4 relative m-2.5" @click="handleSetDefault(v)">
                    <div class="w-full flex justify-between">
                        <span>{{v?.netWorkName}}</span>
                        <div v-if="v?.isWithdrawDefault" class="w-[18px] h-[18px] bg-primary rounded-full"></div>
                    </div>
                    <span class="text-xs mt-3">{{v?.walletAddress}}</span>
                </div>
            </li>
        </ul>
    </van-popup>
    <Password/>
</template>

<script setup>
import { useUsdtStore } from '@/store/usdt.js'

const channelIndex = ref(0)
const {withdrawalList} = storeToRefs(useUsdtStore())
const formState = ref({
    amount: 0,
    id: '',
    password: '',
    walletId: ''
})


const data = ref({
    amountIndex: 0,
})

const showAddressSel= ref(false)

const {res: walletList} = useRequest({
    url: '/user/wallet/list',
})

const network = computed(() => {
    return withdrawalList.value?.[channelIndex.value]?.network
})

const _walletList = computed(() => {
    return walletList.value?.filter(v => {
        return v.network === network.value
    }) || []
})

// 当前选择的提现通道配置 id = walletId
const defaultWallet = computed(() => {
    const wallet = walletList.value?.filter(item => item.network === network.value)?.find(v => v.isWithdrawDefault) || walletList.value?.[0] || {}
    formState.value.walletId = wallet.id
    return wallet
})

// 当前选择的提现通道配置 id = id
const itemData = computed(() => {
    return withdrawalList.value?.[channelIndex.value]
})

const amountOptions = computed(() => {
    return itemData.value?.withdrawalAmountOptionsList?.map(v => {
        return {
            label: v,
            value: v,
        }
    }) || []
})

// user/wallet/update
const handleSetDefault = async (item) => {
    await api_post({
        url: '/user/wallet/update',
        params: {
            ...item,
            isWithdrawDefault: 1,
        },
    })
    walletList.value.forEach(v => {
        v.isWithdrawDefault = v.id === item?.id
    })
    showAddressSel.value = false
}

const disabled = useFormDisabled(formState, ['id', 'password'])

const onValid = (e) => {
    e.preventDefault()

    const { minWithdrawalAmount, maxWithdrawalAmount } = itemData.value
    if (formState.value.amount >= minWithdrawalAmount && formState.value.amount <= maxWithdrawalAmount) {
        onOpenPassword()
    } else {
        showFailToast(t('financial.amount_range_error'))
    }
}

const [ onSubmit, loading ] = useFetchLoading(async (password) => {
    await api_post({
        url: '/withdrawal/withdrawUSDT',
        params: {
            ...formState.value,
            id: itemData.value?.id,
            password: btoa(password),
        },
    })

    showSuccessToast(t('operation.successfully'))
})

const { onOpenPassword, Password } = usePassword(onSubmit)

const { $spot } = storeToRefs(useAccountStore())

// 选择充值金额
watch(() => [data.value.amountIndex, amountOptions.value], ([i]) => {
    formState.value.amount = amountOptions.value?.[i]?.value || ''
}, { immediate: true })

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            deposit: {
                mainnet: '提现主网',
                limit: '单笔限额',
                notice: '*不同协议钱包地址不互通，操作请务必鉴别，转账应严格注意充值对应地址',
                address: '提现地址',
                addAddress: '添加提现地址',
                change: '更换',
                amount: '提现金额',
                rate: '当前汇率',
                inputPlaceholder: '请输入提现金额',
                addressSelect: '地址选择',
                manage: '管理'
            },
            financial: {
                amount_range_error: '金额范围错误'
            },
            operation: {
                successfully: '操作成功'
            }
        },
        [LANGUAGE.zhHK]: {
            deposit: {
                mainnet: '提現主網',
                limit: '單筆限額',
                notice: '*不同協議錢包地址不互通，操作請務必鑒別，轉賬應嚴格注意充值對應地址',
                address: '提現地址',
                addAddress: '添加提現地址',
                change: '更換',
                amount: '提現金額',
                rate: '當前匯率',
                inputPlaceholder: '請輸入提現金額',
                addressSelect: '地址選擇',
                manage: '管理'
            },
            financial: {
                amount_range_error: '金額範圍錯誤'
            },
            operation: {
                successfully: '操作成功'
            }
        },
        [LANGUAGE.enUS]: {
            deposit: {
                mainnet: 'Withdrawal Network',
                limit: 'Limit per Transaction',
                notice: '*Wallet addresses of different protocols are not interoperable. Please carefully verify before transferring.',
                address: 'Withdrawal Address',
                addAddress: 'Add Withdrawal Address',
                change: 'Change',
                amount: 'Withdrawal Amount',
                rate: 'Current Exchange Rate',
                inputPlaceholder: 'Enter Withdrawal amount',
                addressSelect: 'Address Selection',
                manage: 'Manage'
            },
            financial: {
                amount_range_error: 'Invalid amount range'
            },
            operation: {
                successfully: 'Operation Successful'
            }
        }
    }
})

defineOptions({ name: 'withdraw-usdt' })
</script>