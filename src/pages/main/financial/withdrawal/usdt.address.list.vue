<template>
    <c-header :title="t('address.title')" />
    <div class="with-header-container">
        <div v-for="v in addressList" class="flex flex-col items-start justify-center bg-white rounded-lg px-4 py-2.5 relative mb-2.5">
            <div class="w-full flex justify-between">
                <span>{{v?.netWorkName}}</span>
                <span @click="handleDel(v?.id)" class="">
                    <c-icon class="ml-0.25" name="delete" size="22" />
                </span>
            </div>
            <p class="text-xs mt-3">{{v?.walletAddress}}</p>

            <div class="flex justify-between items-center w-full mt-3 border-t border-gray-200 pt-2" @click="handleSetDefault(v)">
                <span>{{t('address.setDefault')}}</span>
                <van-checkbox :checked="Boolean(v?.isWithdrawDefault)"/>
            </div>
        </div>

        <div v-if="addressList?.length === 0" class="flex flex-col items-center text-center text-sm my-25 text-gray-500">
            <img class="w-50 h-50" :src="addressImg" alt="">
            {{t('address.empty')}}
        </div>

        <c-submit
            class="mt-10 mb-4"
            data-aos-delay="200"
            @click="$router.push('/usdt-address/add')"
        >
            {{t('address.add')}}
        </c-submit>
    </div>
</template>

<script setup>
const addressImg = new URL('@skins/templates/_TEMPLATE_/_THEME_/no_address.png', import.meta.url).href

const {res: addressList, onRefresh} = useRequest({
    url: '/user/wallet/list',
})

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            address: {
                title: 'USDT地址管理',
                setDefault: '设为默认',
                empty: '未添USDT地址',
                add: '添加地址'
            }
        },
        [LANGUAGE.zhHK]: {
            address: {
                title: 'USDT地址管理',
                setDefault: '設為默認',
                empty: '未添USDT地址',
                add: '添加地址'
            }
        },
        [LANGUAGE.enUS]: {
            address: {
                title: 'USDT Address Management',
                setDefault: 'Set as Default',
                empty: 'No USDT Address Added',
                add: 'Add Address'
            }
        }
    }
})

const handleSetDefault = async (v) => {
    await api_post({
        url: '/user/wallet/update',
        params: {
            ...v,
            isWithdrawDefault: 1,
        },
    })
    addressList.value.forEach(item => {
        item.isWithdrawDefault = item.id === v?.id
    })
}

const handleDel = async (id) => {
   await api_delete({
        url: `/user/wallet/delete/${id}`,
    })
   await onRefresh()
}

defineOptions({
    name: 'usdtAddressList',
})
</script>