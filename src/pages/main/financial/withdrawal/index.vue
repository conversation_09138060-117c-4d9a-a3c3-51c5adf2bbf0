<template>
    <FinancialPageContainer
        type="withdrawal"
        :title="$t('account.withdrawal')"
        to="/withdrawal/record"
    >
        <template #bank>
            <Bank/>
        </template>
        <template #third>
            <Third/>
        </template>
        <template #usdt>
            <Usdt />
        </template>
        <template #aliPay>
            <AliPay/>
        </template>
    </FinancialPageContainer>
</template>

<script setup>
import FinancialPageContainer from '@/pages/main/financial/components/PageContainer.vue'
import Bank from './bank.vue'
import Third from './third.vue'
import Usdt from './usdt.vue'
import AliPay from './aliPay.vue'

defineOptions({ name: 'withdrawal' })
</script>

<style scoped>

</style>
