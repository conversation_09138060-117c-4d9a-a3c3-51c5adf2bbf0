<template>
    <c-header :title="t('financial.add_bank')"/>

    <form class="with-header-container" @submit="onValid">
        <c-card :title="t('financial.bank_number')">
            <c-input
                data-aos="fade-left"
                data-aos-delay="100"
                :placeholder="t('form.input_placeholder', [ t('financial.bank_number') ])"
                minlength="10"
                maxlength="19"
                inputmode="numeric"
                v-model="formState.bankCardNo"
            />
        </c-card>

        <c-card
            class="mt-4 mb-10"
            no-padding
            data-aos-delay="100"
        >
            <van-form
                label-width="120px"
            >
                <van-field
                    data-aos="fade-left"
                    data-aos-delay="50"
                    :label="t('financial.bank_owner')"
                    :model-value="$profile.realName"
                    autocomplete="off"
                    clearable
                    @clear="$profile.realName = ''"
                />
                <van-field
                    data-aos="fade-left"
                    data-aos-delay="150"
                    :label="t('_bank')"
                    is-link
                    readonly
                    required
                    @click="popup = true"
                    :model-value="selectedBankName"
                />
                <van-field
                    data-aos="fade-left"
                    data-aos-delay="200"
                    :label="t('profile.mobile')"
                    v-model="$profile.mobile"
                    autocomplete="off"
                    clearable
                    @clear="$profile.mobile = ''"
                />
                <van-field
                    data-aos="fade-left"
                    data-aos-delay="250"
                    :label="t('auth.otp')"
                    :placeholder="t('form.input_placeholder', [ t('auth.otp') ])"
                    type="digit"
                    :minlength="6"
                    :maxlength="6"
                    required
                    clearable
                    v-model="formState.smsCode"
                >
                    <template #button>
                        <van-button
                            size="mini"
                            type="primary"
                            :loading="otpLoading"
                            :disabled="otpDisabled"
                            @click="onSendOtp($profile.mobile)"
                        >
                            {{ otpText }}
                        </van-button>
                    </template>
                </van-field>
            </van-form>
        </c-card>

        <c-submit
            data-aos-delay="200"
            :loading
            :disabled="disabled || !selectedBankId"
        />
    </form>

    <van-popup
        position="bottom"
        v-model:show="popup"
    >
        <van-picker
            :loading="bankLoading"
            :columns="filterOptions"
            :columns-field-names="{ text: 'bankName', value: 'id' }"
            :default-index="selIndexInFilter"
            @change="handleSelChange"
        >
            <template #option="{ bankFullName, icon }">
               <div class="flex flex-start w-full ml-5">
                   <img :src="icon" class="w-10 h-10" alt="">
                   <span class="text-title mr-2">{{ bankFullName }}</span>
               </div>
            </template>
            <template #empty>
                <van-empty :description="t('common.empty')"/>
            </template>
            <template #toolbar>
               <div class="w-full flex justify-between items-center mt-2.5 px-2.5 h-45">
                   <span style="color: var(--van-picker-cancel-action-color)" class="text-xs!" @click="popup = false">{{t('_cancel')}}</span>
                   <div class="w-[75%]">
                       <c-input
                           :placeholder="t('_search_placeholder')"
                           v-model="keyword"
                           @compositionstart="isComposing = true"
                           @compositionend="onCompEnd"
                       />
                   </div>
                   <span
                       style="color: var(--van-picker-confirm-action-color)"
                       class="text-xs!"
                       @click="onConfirm"
                   >
                       {{t("_confirm")}}
                   </span>
               </div>
            </template>
        </van-picker>
    </van-popup>
</template>

<script setup>
import { useDebounceFn } from '@vueuse/core'
const { back } = useRouter()
const { $profile } = storeToRefs(useProfileStore()),
    { dispatch_refreshBank } = useBankStore()

const selectedBank = ref(null)
const selectedBankId = computed(() => selectedBank.value?.id ?? 0)
const selectedBankName = computed(() => selectedBank.value?.bankName ?? '')

const selIndexInFilter = ref(0)
const { dispatch_refreshWallet } = useWalletStore()

const formState = ref({
    bankCardNo: '',
    smsCode: '',
})
const keyword = ref('')
const isComposing = ref(false)
const disabled = useFormDisabled(formState)

const { otpText, otpLoading, otpDisabled, onSendOtp } = useOtp({ type: OTP_TYPES.BIND })

const popup = ref(false)
const filterOptions = ref([])

const onConfirm = () => {
    const pick = filterOptions.value[selIndexInFilter.value]
    selectedBank.value = pick ?? null
    popup.value = false
}

const handleSelChange = (value, index) => {
    if (typeof index === 'number') {
        selIndexInFilter.value = index
    } else if (value?.selectedIndexes) {
        selIndexInFilter.value = value.selectedIndexes[0] ?? 0
    } else if (value?.detail?.index != null) {
        selIndexInFilter.value = value.detail.index
    }
}


const { res, loading: bankLoading, run } = useRequest({
    url: '/bank/list',
    initialValues: [],
})

const sourceBanks = computed(() => Array.isArray(res.value) ? res.value : [])
const normalize = (s = '') => s.toString().trim().toLowerCase()

const doFilter = (q) => {
    const kw = normalize(q)
    if (!kw) return sourceBanks.value
    return sourceBanks.value.filter(b => normalize(b?.bankName || '').includes(kw))
}
const applyFilter = () => {
    filterOptions.value = doFilter(keyword.value)
}

const debouncedApply = useDebounceFn(applyFilter, 350)

const onCompEnd = (e) => {
    isComposing.value = false
    const v = e?.target?.value ?? e?.value
    if (typeof v === 'string' && v !== keyword.value) keyword.value = v
    applyFilter()
}

watch([keyword, sourceBanks], () => {
    if (isComposing.value) return
    debouncedApply()
}, { immediate: true })

watch(popup, (show) => {
    if (show) {
        keyword.value = ''
        filterOptions.value = sourceBanks.value
        const i = filterOptions.value.findIndex(b => b.id === selectedBankId.value)
        selIndexInFilter.value = i >= 0 ? i : 0
    }
})

watch(filterOptions, (list) => {
    if (!list?.length) { selIndexInFilter.value = 0; return }
    const i = list.findIndex(b => b.id === selectedBankId.value)
    selIndexInFilter.value = i >= 0 ? i : 0
})


const onValid = (e) => {
    e.preventDefault()

    const { bankCardNo, smsCode } = formState.value
    const systemBankId = selectedBankId.value

    if (!REGULAR.BANK.test(bankCardNo)) return showFailToast(t('financial.bank_error'))
    if (!systemBankId) return showFailToast(t('form.select_placeholder', [ t('_bank') ]))
    if (!REGULAR.OTP.test(smsCode)) return showFailToast(t('auth.otp_error'))

    onSubmit()
}

const [ onSubmit, loading ] = useFetchLoading(async () => {
    const { bankCardNo, smsCode } = formState.value
    const systemBankId = selectedBankId.value

    try {
        await api_post({
            url: '/bank/addUserBank',
            params: {
                bankCardNo,
                systemBankId,
                mobile: $profile.value.mobile,
                smsCode,
            },
        })
        showSuccessToast(t('operation.successfully'))
        back()
    } finally {
        await dispatch_refreshBank()
        await dispatch_refreshWallet()
    }
})


const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _bank: '开户行',
            _search_placeholder: '请输入银行名称搜索',
            _confirm: '确认',
            _cancel: '取消'
        },
        [LANGUAGE.zhHK]: {
            _bank: '开户行',
            _search_placeholder: '請輸入銀行名稱搜尋',
            _confirm: '確認',
            _cancel: '取消'
        },
        [LANGUAGE.enUS]: {
            _bank: 'Opening bank',
            _search_placeholder: 'Search by bank name',
            _confirm: 'Confirm',
            _cancel: 'Cancel'
        },
    },
})

defineOptions({ name: 'bank-add' })
</script>

<style scoped>
:deep(.c-controller__main) {
    min-height: 36px!important;
}
</style>
