<template>
    <form @submit="onValid">
        <c-card
            data-aos-delay="100"
            :title="t('deposit.network')"
        >
            <template #extra>
                <span class="text-sm">{{ t('deposit.limit') }}:{{itemData?.minAmount}}-{{itemData?.maxAmount}}</span>
            </template>
            <c-select
                :columns="usdtList || []"
                :columns-field-names="{
                text: 'netWorkName',
                value: 'network',
            }"
                v-model:index="channelIndex"
            />
            <p class="text-xs mt-2.5">
                {{ t('deposit.notice') }}
            </p>
        </c-card>

        <c-card
            data-aos-delay="200"
            :title="t('deposit.amount')"
            class="mt-4"
        >
            <template #extra>
                <span class="text-sm font-weight-400">{{ t('deposit.rate') }}:1: {{itemData?.exchangeRate}}</span>
            </template>
            <!-- 倍数 -->
            <c-picker
                class="mb-2"
                :loading="loading"
                :options="amountOptions"
                v-model:index="data.amountIndex"
            />
            <!-- 倍数 -->
            <c-input
                data-aos="fade-left"
                data-aos-delay="50"
                :placeholder="t('deposit.placeholder')"
                type="number"
                inputmode="decimal"
                v-model="formState.rechargeAmount"
            />

            <div class="text-sm mt-5">
                {{ t('account.balance') }}
                <c-amount :amount="$spot.usableCash"/>
            </div>
        </c-card>

        <c-submit
            class="mt-10 mb-4"
            data-aos-delay="200"
            :disabled
            :loading
        />
    </form>
</template>

<script setup>
const router = useRouter()

const { $spot } = storeToRefs(useAccountStore())
const channelIndex = ref(0)
const formState = ref({
    id: '',
    rechargeAmount: '',
})

const data = ref({
    amountIndex: 0
})

// 后端返回充值列表
const {res: usdtList} = useRequest({
    url: '/recharge_usdt/list',
})

// 列表项数据
const itemData = computed(() => {
    return usdtList.value?.[channelIndex.value]
})

const amountOptions = computed(() => {
   return itemData.value?.rechargeAmountOptionsList?.map(v => {
        return {
            label: v,
            value: v,
        }
   }) || []
})

const disabled = useFormDisabled(formState, ['id'])

const onValid = (e) => {
    e.preventDefault()

    const { minAmount, maxAmount } = itemData.value
    if (formState.value.rechargeAmount >= minAmount && formState.value.rechargeAmount <= maxAmount) {
        onSubmit()
    } else {
        showFailToast(t('financial.amount_range_error'))
    }
}

const [ onSubmit, loading ] = useFetchLoading(async () => {
   const query =  await api_post({
        url: '/payments/applyUSDT',
        params: {
            ...formState.value,
            id: itemData.value?.id
        },
    })

    formState.value.rechargeAmount = ''
    router.push({name: 'usdt-order', query: {...query, netWorkName: itemData?.value?.netWorkName}})
})

// 选择充值金额
watch(() => [data.value.amountIndex, amountOptions.value], ([i]) => {
    formState.value.rechargeAmount = amountOptions.value?.[i]?.value || ''
})

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            deposit: {
                network: '充值网络',
                limit: '单笔限额',
                notice: '*不同协议钱包地址不互通，操作请务必鉴别，转账应严格注意充值对应地址',
                amount: '充值金额',
                rate: '当前汇率',
                placeholder: '选定金额或输入金额',
                submit: '提交'
            },
            financial: {
                amount_range_error: '金额范围错误'
            }
        },
        [LANGUAGE.zhHK]: {
            deposit: {
                network: '充值網絡',
                limit: '單筆限額',
                notice: '*不同協議錢包地址不互通，操作請務必鑒別，轉賬應嚴格注意充值對應地址',
                amount: '充值金額',
                rate: '當前匯率',
                placeholder: '選定金額或輸入金額',
                submit: '提交'
            },
            financial: {
                amount_range_error: '金額範圍錯誤'
            }
        },
        [LANGUAGE.enUS]: {
            deposit: {
                network: 'Deposit Network',
                limit: 'Limit per Transaction',
                notice: '*Wallet addresses of different protocols are not interoperable. Please carefully verify before transferring.',
                amount: 'Deposit Amount',
                rate: 'Current Exchange Rate',
                placeholder: 'Select or Enter Amount',
                submit: 'Submit'
            },
            financial: {
                amount_range_error: 'Invalid amount range'
            }
        }
    }
})

defineOptions({ name: 'deposit-usdt' })
</script>