<template>
    <c-header :title="t('deposit.title', ['USDT'])" />
    <div class="with-header-container">
        <c-card class="mt-2.5">
           <!--订单号-->
           <c-description :label="t('deposit.orderNo')">{{orderNo}}</c-description>
           <!--充值金额-->
           <c-description :label="t('deposit.amount')">{{orderOriginAmount}}</c-description>
            <!--充币数量-->
           <c-description :label="t('deposit.usdtAmount')">{{usdtAmount}}</c-description>
            <!--汇率-->
           <c-description :label="t('deposit.rate')">{{exchangeRate}}</c-description>
            <!--充币网络-->
           <c-description :label="t('deposit.address')">{{rechargeAddress}}</c-description>
            <!--充值网络-->
           <c-description :label="t('deposit.network')">{{netWorkName}}</c-description>
            <p class="text-xs! mt-6 flex justify-center">{{t('deposit.tip')}}</p>
        </c-card>
        <p class="mt-2.5 text-sm">{{t('deposit.qrCode')}}</p>
        <div class="flex justify-center items-center mt-5">
            <img :src="uri" alt="">
        </div>

        <c-submit
            class="mt-10 mb-4"
            data-aos-delay="200"
            :loading
            @click="onSubmit"
        >
            {{t('deposit.submit')}}
        </c-submit>
    </div>
</template>

<script setup>
import QRCode from 'qrcode'

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            deposit: {
                title: '{0} 充值',
                orderNo: '订单号',
                amount: '充值金额',
                usdtAmount: '充币数量',
                rate: '汇率',
                address: '充币地址',
                network: '充值网络',
                qrCode: '充币二维码',
                tip: '请务必完整复制并仔细核对，以免造成损失',
                submit: '已完成充值，联系客服确认订单'
            }
        },
        [LANGUAGE.zhHK]: {
            deposit: {
                title: '{0} 充值',
                orderNo: '訂單號',
                amount: '充值金額',
                usdtAmount: '充幣數量',
                rate: '匯率',
                address: '充幣地址',
                network: '充值網絡',
                qrCode: '充幣二維碼',
                tip: '請務必完整複製並仔細核對，以免造成損失',
                submit: '已完成充值，聯繫客服確認訂單'
            }
        },
        [LANGUAGE.enUS]: {
            deposit: {
                title: '{0} Deposit',
                orderNo: 'Order No.',
                amount: 'Deposit Amount',
                usdtAmount: 'USDT Amount',
                rate: 'Exchange Rate',
                address: 'Deposit Address',
                network: 'Deposit Network',
                qrCode: 'Deposit QR Code',
                tip: 'Please copy and verify carefully to avoid any loss.',
                submit: 'Deposit Completed, Contact Support to Confirm Order'
            }
        }
    }
})

const { $globalConfig } = storeToRefs(useGlobalStore())
const route = useRoute()
const uri = ref('')
const {exchangeRate, orderNo, id, orderAmount, netWorkName, usdtAmount, orderOriginAmount, rechargeAddress, network} = route.query

const [ onSubmit, loading ] = useFetchLoading(async () => {
    await api_put({
        url: `/payments/update/${id}`,
    })

    // 调整到客服页面
    utils_link($globalConfig.value.service)
})

// 生成二维码
const generateQR = async (str)  => {
    try {
       uri.value = await QRCode.toDataURL(str)
    } catch (err) {
        console.error(err)
    }
}
generateQR(rechargeAddress)

defineOptions({
    name: 'usdtOrderDetail',
})
</script>