<!-- 行情 简况 -->
<template>
    <div class="with-header-container">
        <p class="mb-2.5">
            <span class="text-primary font-sm mr-2.5 ">{{ futuresDetail?.name }}</span>
            <span class="font-sm text-primary bg-tag text-xs px-2.5 py-1">{{ futuresDetail?.market }}</span>
        </p>
        <c-card>
            <c-description-group
                :items="baseInfo"
                :data-source="futuresDetail"
                :labelClass="['text-text']"
                :valueClass="['text-title']"
            >
                <template #prefix>
                    <span class="text-title mb-2.5">品种信息</span>
                </template>

                <template #suffix>
                    <div class="p-2.5 bg-page rounded-md mt-2.5">
                        <c-description-group
                            :items="calcInfo"
                            :data-source="futuresDetail"
                            :labelClass="['text-text']"
                            :valueClass="['text-title']"
                        >
                            <template #prefix>
                                <span class="text-title mb-2.5">保证金试算</span>
                            </template>
                        </c-description-group>
                    </div>
                </template>
            </c-description-group>
        </c-card>

    </div>
</template>

<script setup lang="jsx">
import { FUTURES_CONFIG } from '@/config/index.js'

const { t } = useI18n()

const futuresDetail = reactive({})

const futuresStore = useFuturesStore()
const { $futuresInstrument, $currentFutures } = storeToRefs(futuresStore)

const baseInfo = computed(() => [
    {
        label: '品种',
        value: 'name',
        render: (val) => {
            if (!val) return '-'
            return <span>{val}</span>
        },
    },
    {
        label: '交易所',
        value: 'market',
        render: (market) => {
            if (!market) return '-'
            const titleKey = FUTURES_CONFIG.find((v) => v.value === market)?.title
            return <span>{t(`futures.${titleKey}`)}</span>
        },
    },
    {
        label: '杠杆倍数',
        value: 'multiple',
        render: (multiple) => {
            if (!multiple) return '-'
            return <span>{multiple}</span>
        },
    },
    {
        label: '保证金比例',
        value: 'marginRatio',
        render: (marginRatio) => {
            if (!marginRatio) return '-'
            return <span>{marginRatio}%</span>
        },
    }, {
        label: '最低交易',
        value: 'minTradeQuantity',
        render: (minTradeQuantity) => {
            if (!minTradeQuantity) return '-'
            return <span>{minTradeQuantity.toFixed(1)}手</span>
        },
    }, {
        label: '最高交易',
        value: 'maxTradeQuantity',
        render: (val) => {
            if (!val) return '-'
            return <span>{val.toFixed(1)}手</span>
        },
    }, {
        label: '涨跌幅限制',
        render: () => {
            const getText = () => {
                let temp = '--'
                if (futuresDetail?.maxIncrease !== 0 && futuresDetail?.minIncrease !== 0) {
                    temp = `涨${futuresDetail?.maxIncrease?.toFixed(1)}/跌${futuresDetail?.minIncrease?.toFixed(1)}`
                } else if (futuresDetail?.maxIncrease !== 0) {
                    temp = `涨${futuresDetail?.maxIncrease?.toFixed(1)}`
                } else if (futuresDetail?.minIncrease !== 0) {
                    temp = `跌${futuresDetail?.minIncrease.toFixed(1)}`
                }
                return temp
            }
            return <span>{getText()}</span>
        },
    },
])

const calcInfo = computed(() => [
    {
        label: '最新价',
        value: 'latestPrice',
        render: (val) => {
            if (!val) return '-'
            return <span>{
                val?.toFixed(3)
            }</span>
        },
    },
    {
        label: '杠杆倍数',
        value: 'multiple',
        render: (multiple) => {
            if (!multiple) return '-'
            return <span>{multiple}</span>
        },
    },
    {
        label: '保证金比例',
        value: 'marginRatio',
        render: (marginRatio) => {
            if (!marginRatio) return '-'
            return <span>{marginRatio}%</span>
        },
    }, {
        label: '1手所需保证金',
        labelClass: 'text-title',
        render: () => {
            console.log(futuresDetail.multipleList)

            let temp2 = '--'
            if (+futuresDetail.multiple !== 0 && +futuresDetail?.marginRatio !== 0) {
                temp2 = (futuresDetail.latestPrice * +futuresDetail?.multipleList?.[0] * futuresDetail?.marginRatio * 0.01).toFixed(2)
            }
            return <span class="text-red-text">{temp2}</span>
        },
    },
])

const getConf = async () => {
    const { market, securityType, symbol } = $currentFutures.value
    return await api_get({
        url: '/futures/getConfig',
        params: {
            market,
            securityType,
            symbol,
        },
    })
}

const getFuturesDetail = async () => {
    return await api_get({
        url: '/futures/market/stockInfo',
        params: {
            instrument: $futuresInstrument.value,
        },
    })
}
const tasks = [ getConf(), getFuturesDetail() ]

Promise.all(tasks).then(result => {
    Object.assign(futuresDetail, result[0], result[1])
})

defineOptions({ name: 'gaikuang-details' })
</script>

