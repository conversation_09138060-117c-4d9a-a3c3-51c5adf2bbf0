<template>
    <div>
        <van-skeleton title :row="1" :loading="!name">
            <template #template>
                <div class="flex w-full gap-2.5">
                    <div class="flex-1">
                        <van-skeleton title/>
                        <van-skeleton-paragraph/>
                    </div>
                    <div class="flex-1">
                        <van-skeleton-paragraph/>
                    </div>
                    <div class="flex-1">
                        <van-skeleton-paragraph/>
                    </div>
                </div>
            </template>
            <div
                class="text-center flex items-center justify-between px-2.5 pb-4"
                :class="[
                    utils_amount_color(chg),
                    ($instrument === instrument && optional) ? 'border-primary' : 'border-transparent'
                ]"
            >
                <!-- 名称 -->
                <div class="text-sm flex flex-col flex-1 justify-start">
                    <div class="text-primary flex flex-start">{{ name }}</div>
                    <div class="flex flex-start">
                        <span class="bg-primary marketBlock">{{ market }}</span>
                        <span class="text-[10px] text-text ml-1.25">{{ symbol }}</span>
                    </div>
                </div>
                <!-- 名称 -->

                <!-- 现价 -->
                <div class="text-sm flex-1 flex-middle justify-center">
                    <c-amount
                        :colorful
                        :amount="latestPrice"
                        :precision="3"
                    />
                </div>
                <!-- 现价 -->
                <div class="text-xs flex-middle justify-end gap-2">
                    <!-- 涨跌幅 -->
                    <span
                        class="text-white px-1.5 py-0.5 rounded"
                        :style="{background: backgroundColor}"
                    >
                    <c-amount
                        symbol
                        percent
                        :colorful
                        :amount="gain * 100"
                    />
               </span>
                    <!-- 涨跌幅 -->
                </div>
            </div>
        </van-skeleton>
    </div>
</template>

<script setup>
import { utils_amount_color } from '@/utils/index.js'
import socket from '@/socket2.js'
import { $raise_fall_color } from '@/store'

const { instrument, isVisible, name, isShow, currency, market, symbol, gain } = defineProps({
    optional: Boolean,
    timeline: Array,
    name: String,
    latestPrice: Number,
    market: Number | String,
    symbol: String,
    currency: String,
    chg: Number,
    gain: Number,
    instrument: String,
    isVisible: Boolean, // 判断是否在视口中
    isShow: Boolean, // 判断是否在视口中
    loading: Boolean,
})

const backgroundColor = computed(() => {
    const config = $raise_fall_color.value
    const isUp = gain > 0

    if (config === 'red_green_trend') {
        return isUp ? 'var(--red)' : 'var(--green)'
    } else if (config === 'green_red_trend') {
        return isUp ? 'var(--green)' : 'var(--red)'
    }
    return 'var(--primary)'
})

const emits = defineEmits([ 'select' ])
const colorful = false
const stockStore = useStockStore()
const { $instrument } = storeToRefs(stockStore)
const indexStore = useIndexStore()
const { $indexData } = storeToRefs(indexStore)
const handler = (res) => {
    // const instrument2 = `${res.data?.detail.market}|${res.data?.detail.securityType}|${res.data?.detail.symbol}`
    if (res?.data?.detail) {
        delete res.data?.detail.name
        const detail = res.data?.detail
        const chg = detail.latestPrice * 1 - detail.close * 1

        Object.assign($indexData.value[instrument].details, {
            chg,
            latestPrice: +detail.latestPrice,
            gain: (detail.latestPrice * 1 - detail.close * 1) / +detail.close,
            high: +detail.high,
            open: +detail.open,
            amount: +detail.amount,
            low: +detail.low,
        })
    }

    if (res?.data?.list && res?.data?.list.length) {
        const timeline = $indexData.value[instrument].timeline
        const mergedList = [ ...timeline, ...res.data?.list ]
        filterLatestPerMinute(mergedList, instrument)
    }
}

/**
 * 过滤同一分钟内的多条数据，只使用最新一条。
 * 假设输入数据已经按时间升序排序。
 *
 * @param {Array<{ time: number|null }>} timestamps - K线数据数组
 * @returns {Array} - 过滤后的 K线数据
 */
const filterLatestPerMinute = (timestamps) => {
    if (!Array.isArray(timestamps) || timestamps.length === 0) {
        return []
    }

    const groups = {}

    for (const data of timestamps) {
        if (data.time == null) {
            continue
        }
        const minuteKey = Math.floor(data.time / 60) // 60s = 1min
        if (
            !(minuteKey in groups) ||
            data.time >= groups[minuteKey].time
        ) {
            groups[minuteKey] = data
        }
    }
    const result = Object.values(groups)
    result.sort((a, b) => a.time - b.time)
    $indexData.value[instrument].timeline = result
    // return result
}


const handleUnsubscribe = (e) => {
    console.log(e)
}

onMounted(() => {
    socket.on('market', handler, {
        'type': 'market',
        'action': 'timeLine',
        'params': {
            'operate': 'subscribe',
            'instrument': instrument,
            'period': 'day',
        },
    })
})

onBeforeUnmount(() => {
    socket.on('market', handleUnsubscribe, {
        'type': 'market',
        'action': 'timeLine',
        'params': {
            'operate': 'unsubscribe',
            'instrument': instrument,
            'period': 'day',
        },
    })
})

defineOptions({ name: 'IndexTrendChild' })
</script>

