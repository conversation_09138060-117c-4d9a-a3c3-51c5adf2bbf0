<template>
    <div
        class="p-1 h-[100px] rounded-[5px]"
        :class="[
                    utils_amount_color(chg),
                    ($instrument === instrument && optional) ? 'border-primary' : 'border-transparent'
                ]"
        :style="bgColor"
    >
        <!-- 名称 -->
        <div class="text-sm mb-1.25 text-[#000000] text-sm w-full truncate">
            {{ name }}
        </div>
        <!-- 名称 -->

        <!-- 现价 -->
        <c-amount
            :colorful
            :amount="latestPrice"
        />
        <!-- 现价 -->
        <div class="text-xs mt-2.5 flex-middle gap-2">
            <!-- 涨跌额 -->
            <c-amount
                symbol
                :colorful
                :amount="chg"
            />
            <!-- 涨跌额 -->

            <!-- 涨跌幅 -->
            <c-amount
                symbol
                percent
                :colorful
                :amount="gain * 100"
            />
            <!-- 涨跌幅 -->
        </div>
    </div>
</template>

<script setup>
import * as echarts from 'echarts'
import _ from 'lodash'
import { utils_amount_color } from '@/utils/index.js'
import socket from '@/socket2.js'
import regBg from './red_bg.png'
import greenBg from './green_bg.png'

const { instrument, isVisible, name, isShow, currency, market, symbol, gain } = defineProps({
    optional: Boolean,
    timeline: Array,
    name: String,
    latestPrice: Number,
    market: Number | String,
    symbol: String,
    currency: String,
    chg: Number,
    gain: Number,
    instrument: String,
    isVisible: Boolean, // 判断是否在视口中
    isShow: Boolean, // 判断是否在视口中
})

const emits = defineEmits([ 'select' ])

const colorful = false
const stockStore = useStockStore()
const { $instrument } = storeToRefs(stockStore)
const indexStore = useIndexStore()
const { $indexData } = storeToRefs(indexStore)

// 241 331 391
const handler = (res) => {
    // const instrument2 = `${res.data?.detail.market}|${res.data?.detail.securityType}|${res.data?.detail.symbol}`
    if (res?.data?.detail) {
        delete res.data?.detail.name
        const detail = res.data?.detail
        const chg = detail.latestPrice * 1 - detail.close * 1

        Object.assign($indexData.value[instrument].details, {
            chg,
            latestPrice: +detail.latestPrice,
            gain: (detail.latestPrice * 1 - detail.close * 1) / +detail.close,
            high: +detail.high,
            open: +detail.open,
            amount: +detail.amount,
            low: +detail.low,
        })
    }

    if (res?.data?.list && res?.data?.list.length) {
        const timeline = $indexData.value[instrument].timeline
        const mergedList = [ ...timeline, ...res.data?.list ]
        filterLatestPerMinute(mergedList, instrument)
    }
}

/**
 * 过滤同一分钟内的多条数据，只使用最新一条。
 * 假设输入数据已经按时间升序排序。
 *
 * @param {Array<{ time: number|null }>} timestamps - K线数据数组
 * @returns {Array} - 过滤后的 K线数据
 */
const filterLatestPerMinute = (timestamps) => {
    if (!Array.isArray(timestamps) || timestamps.length === 0) {
        return []
    }

    const groups = {}

    for (const data of timestamps) {
        if (data.time == null) {
            continue
        }
        const minuteKey = Math.floor(data.time / 60) // 60s = 1min
        if (
            !(minuteKey in groups) ||
            data.time >= groups[minuteKey].time
        ) {
            groups[minuteKey] = data
        }
    }
    const result = Object.values(groups)
    result.sort((a, b) => a.time - b.time)
    $indexData.value[instrument].timeline = result
    // return result
}


const handleUnsubscribe = (e) => {
    console.log(e)
}

watch(() => isVisible, (bool) => {
    if (bool) {
        $indexData.value[instrument].isShow = true
        socket.on('market', handler, {
            'type': 'market',
            'action': 'timeLine',
            'params': {
                'operate': 'subscribe',
                'instrument': instrument,
                'period': 'day',
            },
        })
    } else {
        socket.on('market', handleUnsubscribe, {
            'type': 'market',
            'action': 'timeLine',
            'params': {
                'operate': 'unsubscribe',
                'instrument': instrument,
                'period': 'day',
            },
        })
        $indexData.value[instrument].isShow = false
    }
}, { immediate: false, flush: 'post' })

const bgColor = computed(() => {
    if (gain >= 0) {
        return {
            background: `url(${regBg}) no-repeat center/cover`,
        }
    } else {
        return {
            background: `url(${greenBg})`,
        }
    }
})

onBeforeUnmount(() => {
    const keyList = Object.keys($indexData.value)
    keyList?.forEach((key, index) => {
        if ($indexData.value[instrument].isShow && key === instrument) {
            socket.on('market', handleUnsubscribe, {
                'type': 'market',
                'action': 'timeLine',
                'params': {
                    'operate': 'unsubscribe',
                    'instrument': key,
                    'period': 'day',
                },
            })
        }

        $indexData.value[key].isShow = index <= 2
    })
})

defineOptions({ name: 'IndexTrendChild' })
</script>

