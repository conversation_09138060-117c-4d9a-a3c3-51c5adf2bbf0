<template>
    <!-- 外层容器也给 min-w-0，防止祖先限制 -->
    <div :class="[ inline ? 'h-5 flex-middle gap-1 min-w-0' : 'w-full min-w-0' ]">
        <!-- 左侧文本块 可压缩区：flex-1 + basis-0 + min-w-0 + truncate -->
        <div
            class="text-title text-xs h-5 leading-5 font-semibold
             flex-1 basis-0 min-w-0
             truncate !whitespace-nowrap !overflow-hidden !text-ellipsis"
        >
            {{ name }}
        </div>

        <!-- 右侧固定宽度：不参与收缩 -->
        <div class="flex-middle gap-1 shrink-0">
            <div class="marketBlock" v-if="market">{{ market }}</div>
            <div class="text-[9px]">{{ symbol }}</div>
        </div>
    </div>
</template>


<script setup>
// import { stockMarketDict } from '@/config/index.js'

defineProps({
    name: String,
    market: {
        type: String,
        required: true,
    },
    symbol: {
        type: String,
        required: true,
    },
    inline: Boolean,
})

defineOptions({ name: 'StockBaseInfo' })
</script>

<style scoped>

</style>
