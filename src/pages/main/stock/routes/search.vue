<template>
    <div class="h-(--header-height) flex-between gap-2.5 px-4">
        <Search
            class="flex-1"
            v-model="content"
            @search="onSearch"
        />

        <div class="text-link" @click="$router.back">
            {{ $t('operation.cancel') }}
        </div>
    </div>

    <div class="with-header-container flex flex-col gap-2">
        <div v-show="!content">
            {{ t('history') }}
        </div>

        <div :class="{ 'history-container': !content }">
            <template v-if="!renderList.length && content">
                <van-empty :description="$t('common.empty')"/>
            </template>

            <template v-else>
                <div
                    class="py-2 flex-between gap-4 border-b border-border"
                    v-for="item in renderList"
                    :key="item.symbol"
                >
                    <StockBaseInfo
                        class="flex-1"
                        :name="item.name"
                        :market="item.market"
                        :symbol="item.symbol"
                        @click="onCheck(item)"
                    />

                    <van-icon
                        name="cross"
                        v-if="!content"
                        @click="onDelete(item.symbol)"
                    />
                    <WatchlistButton
                        v-else
                        :isWatchlist="item?.isWatchlist"
                        :record="item"
                        :loading="collectLoading"
                        @toggle="handleWatchlistToggle"
                    />
                </div>
            </template>
        </div>

        <div
            class="text-center text-link text-xs"
            v-show="!content"
            @click="history = [];"
        >
            {{ t('clear') }}
        </div>
    </div>
</template>

<script setup lang="jsx">
import _ from 'lodash'
import Search from '@/components/Search/index.vue'
import StockBaseInfo from '@/pages/main/stock/components/StockBaseInfo.vue'
import { STOCK_ROUTE } from '@/config/index.js'

const route = useRoute()
const loading = ref(false)
const options = ref([])
const { $stockActiveTab } = storeToRefs(useStockStore())
const { dispatch_checkStock } = useStockStore()
const currentPage = ref(1)
const {$futuresActiveTab} = storeToRefs(useFuturesStore())
const { dispatch_checkFutures } = useFuturesStore()
const collectLoading = ref(false)
const accountStore = useAccountStore()
const { $tradePageContractId, $contract } = storeToRefs(accountStore)

const market = computed(() => $contract.value?.find(v => +v?.id === +$tradePageContractId.value)?.marketType)

const content = ref('')

const history = useSessionStorage('searchHistory', [])
const renderList = computed(() => content.value ? options.value : history.value)

// Watchlist button component
const WatchlistButton = ({ isWatchlist, record, loading }, { emit }) => {
    const buttonClass = [
        'flex items-center gap-2 text-primary px-2 py-1 cursor-pointer rounded-sm transition-colors h-[30px] bg-[rgb(253,217,219)]',
    ]

    const handleClick = (event) => {
        event.stopPropagation()
        emit('toggle', record)   // 🔹 改成自定义事件
    }

    return (
        <div onClick={handleClick} class={buttonClass}>
            {
                isWatchlist ? <c-icon name="heart" color={'#C92C31'} /> :
                <c-icon name="heart-o" color={'#C92C31'} />
            }
            {
               <span class="text-sm text-[#C92C31]">
                   {isWatchlist ? t('selected') : t('favorites')}
               </span>
            }
        </div>
    )
}

// Rewritten onCollectRaw function
const handleWatchlistToggle = async (stock) => {
    if (collectLoading.value) return

    collectLoading.value = true
    try {
        if (stock.isWatchlist) {
            // Remove from watchlist
            await api_delete({
                url: '/symbol/watchlist/deleteBySymbol',
                params: {
                    market: stock.market,
                    securityType: stock.securityType,
                    symbol: stock.symbol,
                    // bool: 1 // 1 0
                },
            })
            updateFav(stock)
        } else {
            // Add to watchlist
            await api_post({
                url: '/symbol/watchlist/add',
                params: {
                    market: stock.market,
                    securityType: stock.securityType,
                    symbol: stock.symbol,
                },
            })
            updateFav(stock)
        }
    } catch (error) {
        console.error('Watchlist operation failed:', error)
        showSuccessToast(t('operation.failed'))
    } finally {
        collectLoading.value = false
    }
}

const updateFav = (stock) => {
    // Update the stock's watchlist status in the options array
    const stockIndex = options.value.findIndex(item => item?.symbol === stock.symbol && item?.market === stock?.market && item?.securityType === stock?.securityType)
    options.value[stockIndex] = { ...options.value[stockIndex], isWatchlist: !options.value[stockIndex].isWatchlist }
}

// 搜索股票 /symbol/search?keyword=ZIONL
const onSearch = _.debounce(async (query) => {
    options.value = []
    loading.value = true
    currentPage.value = 1 // Reset pagination on new search

    try {
        if (query) {
            const result = await api_get({
                url: '/symbol/search',
                params: {
                    keyword: query,
                },
            })
            if (result) {
                // Store all results but only display paginated portion
                options.value = [...result]
            }
        } else {
            options.value = []
        }
    } finally {
        loading.value = false
    }
}, 300)

const onCheck = (stock) => {
    // 如果是期货去期货详情 如果是股票去股票详情

    if (stock.securityType === '4') {
        // 期货
        if (route.params.globalSearch) {
            // 顶部搜索走这个逻辑
            dispatch_checkFutures(stock, { isReplace: true })
        } else {
            // 详情页里的搜索走这个逻辑
            $futuresActiveTab.value = "futures-transaction"
            dispatch_checkFutures(stock, { isBack: true })
        }
    } else {
        // 股票
        history.value = _.uniqBy([ stock, ...history.value ], 'symbol')
        if (route.params.globalSearch) {
            // 顶部搜索走这个逻辑
            dispatch_checkStock(stock, { isReplace: true })
        } else {
            // 详情页里的搜索走这个逻辑
            $stockActiveTab.value = STOCK_ROUTE.TRANSACTION
            dispatch_checkStock(stock, { isBack: true })
        }
    }
}

const onDelete = symbol => {
    history.value = _.filter(history.value, e => e.symbol !== symbol)
}

useRequest({
    url: '/symbol/blacklist',
    onSuccess: res => {
        // 过滤黑名单不展示
        history.value = _.filter(history.value, e => !res.includes(e.instrument))
    },
    onErr: () => {
        history.value = []
    },
})

const { t } = useI18n({
    useScope: 'local',
    messages: {
        [LANGUAGE.zhCN]: {
            history: '历史搜索记录',
            clear: '清空搜索记录',
            loading: '加载中',
            favorites: '加自选',
            selected: '已选'
        },
        [LANGUAGE.zhHK]: {
            history: '历史搜索记录',
            clear: '清空搜索记录',
            loading: '載入中',
            favorites: '加自選',
            selected: '已選'
        },
        [LANGUAGE.enUS]: {
            history: 'History',
            clear: 'Clear',
            loading: 'Loading',
            favorites: 'Favorites',
            selected: 'Selected'
        },
    },
})

defineOptions({ name: 'stock-search' })
</script>

<style scoped>
.history-container {
    /*
        历史搜索记录 24
        清空搜索记录 16
        Gap 8
    */
    max-height: calc(100% - 24px - 16px - 8px);
}
</style>
