<template>
    <van-popup
        class="p-4 overflow-hidden"
        position="bottom"
        v-model:show="show"
    >
        <div class="flex-middle gap-2.5">
            <div class="marketBlock">
                {{ stockCountryDict.get(dataSource.market) }}
            </div>

            <div class="text-title">
                {{ dataSource.symbolName }}
                ({{ dataSource.symbol }})
            </div>
        </div>

        <c-description-group
            :columns="2"
            :data-source
            :items="details"
            :value-format="utils_currency"
        />

        <!-- 持仓时间 -->
        <c-description
            v-if="!isEntrust"
            :label="t('stock.position.hold_time')"
            :value="dataSource.createTime"
        />
        <!-- 持仓时间 -->

        <!-- 委托时间 -->
        <c-description
            v-if="isEntrust"
            :label="t('stock.entrust.create_time')"
            :value="dataSource.tradeTime"
        />
        <!-- 委托时间 -->

        <div class="flex gap-3">
            <div class="flex-1 h-[140px]">
                <Loading :loading>
                    <c-echarts
                        :option
                    />
                </Loading>
            </div>

            <div class="text-primary text-sm">
                <div
                    class="leading-7 whitespace-nowrap"
                    v-for="({ title, key, icon, handler }) in operation"
                    :key
                    @click="handler(key)"
                >
                    <van-icon :name="icon"/>
                    <span class="ml-2">
                        {{ title }}
                    </span>
                </div>
            </div>
        </div>
    </van-popup>
</template>

<script setup>
import * as echarts from 'echarts'

import Loading from '@/components/Loading/index.vue'
import { utils_currency } from '@/utils'
import { ENTRUST_STATUS_DICT, getAction, stockCountryDict, tradeTypeDict } from '@/config/index.js'
import { spotActiveTab } from '@/store/account.js'
import { accountActiveTab } from '@/store'
import { useFuturesStore } from '@/store/futures.js'

const futuresStore = useFuturesStore()
const { dispatch_checkFutures } = futuresStore

const route = useRoute()

const showActionBtn = computed(() => {
    // /futures/transaction
    return (spotActiveTab.value === 5 && accountActiveTab.value === 'spot') || route.path === '/futures/transaction'
})

const { push } = useRouter(),
    { params: { id } } = useRoute()

const { dataSource, type } = defineProps({
    dataSource: {
        type: Object,
        required: true,
    },
    type: {
        type: String,
        required: true,
    },
})

const show = defineModel({
    type: Boolean,
    required: true,
})

const emits = defineEmits([ 'operation', 'refresh' ])

const { dispatch_checkStock } = useStockStore()

const { t } = useI18n({
    useScope: 'global',
})

watch(show, val => {
    if (val) {
        run({
            instrument: [ dataSource.market, dataSource.securityType, dataSource.symbol ].join('|'),
            period: '5day',
        })
    }
})

const isEntrust = computed(() => type === 'entrust' || type === 'deal')

const option = ref({})

const [ primaryColor ] = useCssVariable([ 'primary' ])

const { loading, run } = useRequest({
    url: API_PATH.STOCK_TIME_LINE,
    manual: true,
    cancellable: false,
    onSuccess: res => {
        const { list } = res

        const prices = [],
            volumes = [],
            avprice = [],
            times = []

        list.forEach(e => {
            const { avgPrice, price, volume, time } = e

            prices.push(price)
            volumes.push(volume)
            avprice.push(avgPrice)
            times.push(time * 1000)
        })

        option.value = {
            grid: {
                id: 'price',
                left: 0,
                right: 0,
                top: 20,
                height: 140,
            },
            xAxis: {
                data: times,
                boundaryGap: false,
                axisLine: false,
                axisTick: false,
                axisLabel: false,
            },
            yAxis: {
                scale: true,
                show: false,
            },
            series: [
                {
                    name: 'Price',
                    type: 'line',
                    data: prices,
                    smooth: true,
                    showSymbol: false,
                    lineStyle: {
                        width: 1,
                        color: primaryColor,
                    },
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: 'rgba(39, 122, 248, .5)' }, // 顶部颜色
                            { offset: 1, color: 'rgba(39, 122, 248, 0)' }, // 底部颜色
                        ]),
                    },
                },
                {
                    name: 'Average',
                    type: 'line',
                    data: avprice,
                    smooth: true,
                    showSymbol: false,
                    lineStyle: {
                        width: 1,
                        color: '#F6A445',
                    },
                },
            ],
        }
    },
})

const entrustDetails = [
    { label: t('stock.entrust.price'), value: 'tradePrice' },
    { label: t('stock.market_price'), value: 'stockPrice' },
    { label: t('stock.entrust.deal_quantity'), value: 'dealNum' },
    { label: t('stock.entrust.total'), value: 'tradeNum' },
    {
        label: t('stock.entrust.direction'),
        value: 'direction',
        render: val => {
            const { color } = tradeTypeDict(val)
            const  label  = getAction(dataSource.direction, dataSource.tradeType)
            return h(
                'span',
                {
                    class: `text-${color}`,
                },
                label,
            )
        },
    },
    {
        label: t('common.status'),
        value: 'status',
        render: val => {
            const { label, color } = ENTRUST_STATUS_DICT[val]

            return h(
                'span',
                {
                    class: `text-${color}`,
                },
                label,
            )
        },
    },
]

const positionDetails = [
    { label: t('stock.position.available'), value: 'restNum' },
    { label: t('stock.position.average'), value: 'buyAvgPrice' },
    { label: t('stock.position.total'), value: 'buyTotalNum' },
    { label: t('stock.current_price'), value: 'stockPrice' },
    { label: t('account.earnings'), value: 'floatingProfitLoss' },
    { label: t('stock.market_value'), value: 'marketValue' },
    { label: t('stock.position.earnings_rate'), value: 'floatingProfitLossRate', render: val => `${(val)?.toFixed(2)}%` },
    // { label: t('stock.position.hold_time'), value: 'createTime' },
]

const details = computed(() => isEntrust.value ? entrustDetails : positionDetails)

const onRevoke = useEntrustRevoke(() => {
    show.value = false
    emits('refresh')
})

const operation = computed(() => {
    const quotes = { // 行情
        title: t('stock.quotes'),
        key: 'quotes',
        icon: 'chart-trending-o',
        handler: showActionBtn.value ? () => { // 期货
            sessionStorage.setItem('futuresDetailsTab', 'introduction')
            dispatch_checkFutures(dataSource, { route: 'transaction' })
        } : (key) => { // 股指
            emits('operation', key, dataSource)
            dispatch_checkStock(dataSource)
        },
    }

    if (isEntrust.value) {
        const base = [
            quotes,
            { // 交易
                title: t('stock.transition'),
                key: 'transition',
                icon: 'balance-list-o',
                handler: showActionBtn.value ? () => { // 期货 - 交易
                    sessionStorage.setItem('futuresDetailsTab', 'quotes')
                    dispatch_checkFutures(dataSource, { routeName: 'transaction' })
                } : (key) => { // 股指
                    emits('operation', key, dataSource)
                    dispatch_checkStock(dataSource, {
                        routeName: STOCK_ROUTE.TRANSACTION,
                        params: { contractId: id },
                    })
                },
            },
        ]
        // 详情
        if (type === 'deal') {
            base.push({
                title: t('common.details'),
                key: 'revoke',
                icon: 'description-o',
                handler: (key) => {
                    emits('operation', key, dataSource)
                    push(`/entrust/${dataSource.id}`)
                },
            })
        }
        // 撤单
        if (type !== 'deal') {
            base.splice(0, 0, {
                title: t('stock.entrust.revoke'),
                key: 'revoke',
                icon: 'revoke',
                handler: async (key) => {
                    emits('operation', key, dataSource)
                    await onRevoke(dataSource.id)
                },
            })
        }

        return base
    } else {
        return [
            quotes,
            {
                title: t('dict.买入'),
                key: 'buy',
                icon: 'shopping-cart-o',
                handler: (key) => {
                    emits('operation', key, dataSource)
                },
            },
            {
                title: t('dict.卖出'),
                key: 'sell',
                icon: 'cash-back-record-o',
                handler: (key) => {
                    emits('operation', key, dataSource)
                },
            },
            // {
            //     title: t('stock.transaction.close'),
            //     key: 'close',
            //     icon: 'chart-trending-o',
            //     handler: (key) => {
            //         emits('operation', key, dataSource)
            //     },
            // },
        ]
    }
})

defineOptions({ name: 'StockPopup' })
</script>

<style scoped>

</style>
