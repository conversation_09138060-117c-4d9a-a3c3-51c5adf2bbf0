<template>
    <van-tabs
        class="stock-tabs h-full"
        v-model:active="tabActive"
    >
        <div class="sticky z-50 top-0 bg-bg px-4 py-2" :class="$raise_fall.color">
            <div class="flex-middle gap-2.5 text-xs mb-2.5" :class="stockStatus.color">
                <div>{{ stockStatus.status }}</div>
                <div>{{ stockStatus.time }}</div>
            </div>

            <div class="flex-middle gap-4">
                <StockPrice/>

                <div class="ml-auto flex-1 flex-middle gap-2">
                    <div class="flex-1 w-1 text-right">{{ $stock.name }}</div>

                    <c-icon v-if="$stock.market" :name="stockCountryDict.get($stock.market)"/>
                </div>
            </div>
        </div>

        <van-tab name="quotes" :title="t('stock.quotes')">
            <!-- 行情 -->
            <QuotesTab/>
        </van-tab>
        <van-tab
            v-if="+type === 1"
            name="financial"
            :title="t('_financial')"
        >
            <!-- 资金 -->
            <FinancialTab/>
        </van-tab>
        <van-tab name="news" :title="t('header.news')">
            <!-- 新闻 -->
            <NewsTab/>
        </van-tab>
        <van-tab
            v-if="+type === 1"
            name="introduction"
            :title="t('_introduction')"
        >
            <!-- 概括 -->
            <IntroductionTab/>
        </van-tab>
    </van-tabs>
</template>

<script setup>
import _ from 'lodash'

import { stockCountryDict } from '@/config/index.js'
import QuotesTab from './components/QuotesTab.vue'
import FinancialTab from './components/FinancialTab.vue'
import NewsTab from './components/NewsTab.vue'
import IntroductionTab from './components/IntroductionTab.vue'
import StockPrice from '../../components/StockPrice.vue'

const { params: { type } } = useRoute()

const { $currentStock, $stock, $raise_fall } = storeToRefs(useStockStore())

// 股票状态
const { res: stockStatusRes } = useRequest({
    url: '/market/status',
    params: _.pick($currentStock.value, [ 'market', 'securityType', 'symbol' ]),
    initialValues: {
        statusStr: '',
        closeTime: 0,
        openTime: 0,
        status: 0,
    },
    sessionKey: 'stockStatus',
})

const stockStatus = computed(() => {
    const { statusStr, status, closeTime, openTime } = stockStatusRes.value

    let time, color

    switch (status) {
        // 待开盘
        case 0:
            color = 'text-special'
            time = openTime
            break
        // 盘前交易
        case 1:
            color = 'text-special'
            time = openTime
            break
        // 交易中
        case 2:
            color = 'text-primary'
            time = $stock.value.latestTime
            break
        // 盘后竞价
        case 3:
            color = 'text-special'
            time = closeTime
            break
        // 已停盘
        case 4:
            color = 'text-text'
            time = closeTime
            break
        // 午间休市
        case 6:
            color = 'text-special'
            time = openTime
            break
    }

    return {
        status: statusStr,
        color,
        time: utils_time(time * 1000),
    }
})

const tabActive = useSessionStorage('stockDetailsTab', 'quotes')

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _introduction: '简况',
            _financial: '资金',
        },
        [LANGUAGE.zhHK]: {
            _introduction: '简况',
            _financial: '资金',
        },
        [LANGUAGE.enUS]: {
            _introduction: 'Introduction',
            _financial: 'Finance',
        },
    },
})

defineOptions({ name: 'stock-details' })
</script>

<style scoped>
</style>
