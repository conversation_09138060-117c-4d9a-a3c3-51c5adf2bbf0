import { utils_jump, utils_link } from '@/utils'
import { STOCK_CONFIG } from '@/config'
import { $activityActiveTab, NOTICE_TYPE } from '@/pages/main/activity/store.js'
import NewsList from '@/pages/main/news/components/_TEMPLATE_/NewsList.vue'
import IndexTrend from '@/pages/main/quotes/routes/index/components/IndexTrend.vue'
import StockHot from '../components/StockHot.vue'
import FuturesList from '@/pages/main/quotes/routes/futures/index.vue'

export const useHome = () => {
    const { $notice } = storeToRefs(useNoticeStore()),
        { dispatch_checkStock } = useStockStore(),
        {dispatch_checkFutures} = useFuturesStore(),
        { $marketType } = storeToRefs(useQuotesStore()),
        { $globalConfig } = storeToRefs(useGlobalStore())
    // System Config Store
    const sysConfigStore = useSysConfigStore()
    const { showIndexSection, showFuturesSection } = storeToRefs(sysConfigStore)

    // 轮播图
    const {
        res: carousel, initial: carouselInitial,
    } = useRequest({
        // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/Carousel%20function%20controller/getBannerPageUsingGET_1
        url: '/banner/page', initialValues: [],
    })

    const columns = useStockColumns()

    const {
        list: collectList,
        refreshLoading: collectRefreshLoading,
        onRefresh: onRefreshCollect,
        onFilter: onFilterCollect,
    } = useStockCollect({}, {
        paginationOptions: {
            pageSize: 5,
        },
    })

    const stockTabActive2 = ref(showIndexSection.value ? 0 : 2)

    const onRedirectIndexDetails = (instrument) => {
        const [ market, securityType, symbol ] = instrument.split('|')
        dispatch_checkStock({ market, securityType, symbol })
    }

    const marqueeRefRef = useTemplateRef('marqueeRef')
    const onResetMarquee = () => {
        marqueeRefRef.value?.reset()
    }
    watch($theme, onResetMarquee)
    watch($notice, onResetMarquee)

    const { t } = useI18n({
        useScope: 'global', messages: {
            [LANGUAGE.zhCN]: {
                _ai: 'AI分析',
                _vip: 'VIP',
            }, [LANGUAGE.zhHK]: {
                _ai: 'AI分析',
                _vip: 'VIP',
            }, [LANGUAGE.enUS]: {
                _ai: 'AI',
                _vip: 'VIP',
            },
        },
    })

    const handleRowClick = async (row) => {
        if(row?.securityType === '4') {
            await dispatch_checkFutures(row)
        } else {
            await dispatch_checkStock(row)
        }
    }

    return {
        t,
        onRedirectIndexDetails,
        stockTabActive2,
        collectList,
        collectRefreshLoading,
        onFilterCollect,
        columns,
        onRefreshCollect,
        carousel,
        carouselInitial,
        showIndexSection,
        showFuturesSection,
        sysConfigStore,
        $globalConfig,
        $marketType,
        dispatch_checkStock,
        dispatch_checkFutures,
        handleRowClick,
        $notice,
        IndexTrend,
        utils_jump,
        STOCK_CONFIG,
        $activityActiveTab,
        NOTICE_TYPE,
        NewsList,
        StockHot,
        FuturesList,
    }
}
