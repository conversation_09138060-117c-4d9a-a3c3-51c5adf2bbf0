<template>
    <div class="home with-header-container">
        <!-- 滚动公告 -->
        <van-notice-bar
            ref="marqueeRef"
            data-aos="fade-left"
            data-aos-delay="50"
            class="mt-1 rounded-md"
            scrollable
            background="rgba(255, 255, 255, 0.7)"
            color="#0052FF"
        >
            <template #left-icon>
                <img src="./left-icon.svg" alt="icon" class="w-6.26 h-6.25 ml-1"/>
            </template>
            <div class="flex-middle gap-4">
                <div
                    v-for="({ id, content }) in $notice.marquee"
                    :key="id"
                    v-html="content"
                />
            </div>
        </van-notice-bar>
        <!-- 滚动公告 -->

        <!-- 轮播图 -->
        <van-skeleton :loading="!carouselInitial">
            <template #template>
                <van-skeleton-image data-aos="fade-left" class="w-full! h-[126px]! mt-3 mb-4"/>
            </template>

            <van-swipe class="mt-4" data-aos="fade-left">
                <van-swipe-item
                    v-for="({ id, title, imageUrl, jumpType, jumpUrl }) in carousel"
                    :key="id"
                    @click="utils_jump(jumpType, jumpUrl)"
                >
                    <van-image
                        class="w-full h-[126px] rounded-lg overflow-hidden"
                        :src="imageUrl"
                        :alt="title"
                    />
                </van-swipe-item>
            </van-swipe>
        </van-skeleton>
        <!-- 轮播图 -->

        <div class="flex-between mt-2 gap-5 bg-white overflow-hidden h-[71px] rounded-lg px-8 py-1">
            <div
                class="flex flex-col items-center justify-center h-full"
                data-aos="fade-left"
                v-for="({ title, icon, to, handler }, index) in nav"
                :data-aos-delay="`${index * 50}`"
                :key="index"
                @click="handler ? handler() : $router.push(to)"
            >
                <c-icon :name="icon" size="34"/>
                <div class="text-[#525A79] mt-1 text-sm whitespace-nowrap">
                    {{ title }}
                </div>
            </div>
        </div>

        <div class="rounded-lg bg-white mt-2.5 p-4 relative overflow-hidden rounded-xl bg-white shadow">
            <div
                class="absolute -top-4 -left-4 w-25 h-25 bg-[radial-gradient(circle_at_top_left,_#DC2626_0%,_transparent_70%)] opacity-40 pointer-events-none"></div>
            <p class="flex items-center gap-2 text-title font-bold text-sm">
                <c-icon name="jingxuan" size="24"/>
                精选股票
            </p>
            <div
                class="text-sm flex gap-5 text-title h-10 items-center"
                data-aos="fade-left"
                data-aos-delay="100"
                shrink
            >
            <span class="tab" v-if="showIndexSection" :class="stockTabActive2 === 0 ? 'activeTav' : 'unActive'"
                  @click="stockTabActive2 = 0">{{
                    t('stock.index')
                }}</span>
                <span class="tab" v-if="showIndexSection" :class="stockTabActive2 === 1 ? 'activeTav' : 'unActive'"
                      @click="stockTabActive2 = 1">{{
                        t('stock.title')
                    }}</span>
                <span class="tab" v-if="showFuturesSection" :class="stockTabActive2 === 2 ? 'activeTav' : 'unActive'"
                      @click="stockTabActive2 = 2">{{
                        t('stock.futures')
                    }}</span>
                <span class=tab :class="stockTabActive2 === 3 ? 'activeTav' : 'unActive'" @click="stockTabActive2 = 3">{{
                        t('stock.collect')
                    }}</span>
            </div>

            <!--股指-->
            <IndexTrend
                v-if="stockTabActive2 === 0 && showIndexSection"
                class="mt-2.5"
                @select="onRedirectIndexDetails"
            />

            <!--证券-->
            <van-tabs
                v-if="stockTabActive2 === 1 && showIndexSection"
                shrink
                v-model:active="$marketType"
                style="--van-tab-font-size: 12px;"
            >
                <template #nav-right>
                    <router-link to="/quotes/stock" class="text-xs ml-auto">
                        <span>{{ t('common.more') }}</span>
                        <van-icon name="arrow"/>
                    </router-link>
                </template>

                <van-tab
                    class="pt-2.5"
                    v-for="(_, symbol) in STOCK_CONFIG"
                    :key="symbol"
                    :name="symbol"
                    :title="t(`stock.${symbol}`)"
                >
                    <StockHot :market="symbol"/>
                </van-tab>
            </van-tabs>

            <FuturesList v-if="stockTabActive2 === 2 && showFuturesSection"/>

            <!--自选-->
            <c-table
                v-if="stockTabActive2 === 3"
                data-aos="fade-left"
                :columns
                :onRefresh="onRefreshCollect"
                :data-source="collectList"
                v-model:refresh-loading="collectRefreshLoading"
                @row-click="handleRowClick"
                @filter="onFilterCollect"
            />
        </div>

        <!--新闻-->
        <div class="rounded-lg bg-white mt-2.5 p-4 relative overflow-hidden rounded-xl bg-white shadow">
            <div
                class="absolute -top-4 -left-4 w-25 h-25 bg-[radial-gradient(circle_at_top_left,_#3B82F6_0%,_transparent_70%)] opacity-40 pointer-events-none"></div>
            <p class="flex items-center gap-2 text-title font-bold text-sm mb-3">
                <c-icon name="zixun" size="24"/>
                今日资讯
            </p>
            <NewsList/>
        </div>

    </div>
</template>

<script setup>
import { utils_jump, utils_link } from '@/utils'
import { STOCK_CONFIG } from '@/config'
import NewsList from '@/pages/main/news/components/_TEMPLATE_/NewsList.vue'
import IndexTrend from '@/pages/main/quotes/routes/index/components/IndexTrend.GP.vue'
import StockHot from './components/StockHot.vue'
import FuturesList from '@/pages/main/quotes/routes/futures/index.vue'
import { useHome } from '@/pages/main/home/<USER>/useHome.js'

const { $notice } = storeToRefs(useNoticeStore()),
    { dispatch_checkStock } = useStockStore(),
    { $marketType } = storeToRefs(useQuotesStore()),
    { $globalConfig } = storeToRefs(useGlobalStore())
// System Config Store
const sysConfigStore = useSysConfigStore()
const { showIndexSection, showFuturesSection } = storeToRefs(sysConfigStore)

const onDownload = useDownload()

const { handleRowClick } = useHome()

// 轮播图
const {
    res: carousel,
    initial: carouselInitial,
} = useRequest({
    // https://member.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/Carousel%20function%20controller/getBannerPageUsingGET_1
    url: '/banner/page',
    initialValues: [],
})

const columns = useStockColumns()

const {
    list: collectList,
    refreshLoading: collectRefreshLoading,
    onRefresh: onRefreshCollect,
    onFilter: onFilterCollect,
} = useStockCollect({}, {
    paginationOptions: {
        pageSize: 5,
    },
})

const stockTabActive2 = ref(showIndexSection.value ? 0 : 2)

const onRedirectIndexDetails = (instrument) => {
    const [ market, securityType, symbol ] = instrument.split('|')
    dispatch_checkStock({ market, securityType, symbol })
}

watch([() => $theme, () => $notice], () => {
    onResetMarquee()
})

const nav = computed(() => [
    {
        title: t('header.about'),
        icon: 'about_us',
        to: '/about',
    },
    {
        title: t('_ai'),
        icon: 'ai',
        to: '/ai',
    },
    {
        title: t('header.service'),
        icon: 'customer',
        handler: () => {
            utils_link($globalConfig.value.service)
        },
    },
])

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _ai: 'AI分析',
        },
        [LANGUAGE.zhHK]: {
            _ai: 'AI分析',
        },
        [LANGUAGE.enUS]: {
            _ai: 'AI',
        },
    },
})

defineOptions({ name: 'Home' })
</script>

<style scoped>
.tab {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    height: 22px;
    line-height: 22px;
    border-radius: 22px;
    padding: 6px 15px;
}

.activeTav {

    background-color: #CDE8FF;
    color: #0052FF;
}

.unActive {
    background: #F2F2F2;
    color: #525A79;
}

</style>
