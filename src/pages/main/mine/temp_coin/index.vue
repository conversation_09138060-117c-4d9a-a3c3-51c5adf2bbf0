<template>
    <van-pull-refresh
        class="profile h-full overflow-auto p-4"
        :disabled="!$isLogin"
        :model-value="loading"
        @refresh="_onRefresh"
        v-bind:style="backgroundStyle"
    >
        <div class="h-full overflow-y-scroll rounded-lg ">
            <!-- 基础信息 -->
            <div data-aos="fade-left">
                <div v-if="!$isLogin" class="flex">
                    <div class="w-3/5 ml-auto flex gap-2.5">
                        <van-button
                            round
                            block
                            size="small"
                            type="primary"
                            to="/auth/register"
                        >
                            {{ t('auth.register') }}
                        </van-button>

                        <van-button
                            round
                            block
                            size="small"
                            type="primary"
                            to="/auth"
                        >
                            {{ t('auth.login') }}
                        </van-button>
                    </div>
                </div>

                <div v-else class="flex-middle gap-2.5 text-sm">
                    <c-avatar
                        :avatar="$profile.avatar"
                        @click="$router.push('/avatar')"
                    />

                    <div class="text-text3" @click="$router.push('/profile')">
                        <div class="normal-case flex-middle gap-2.5">
                            <span>{{ $profile.nickname }}</span>
                            <img
                                v-if="$profile.auth"
                                :src="verifyIcon[locale]"
                                alt="verify"
                                class="h-4"
                            >
                        </div>
                        <div>{{ $profile.mobile }}</div>
                        <div class="normal-case">uid: {{ $profile.uid }}</div>
                    </div>

                    <!-- TODO: 图标不够-->
                    <c-icon
                        class="ml-auto"
                        prefix="mine"
                        size="40"
                        :name="`vip_${$profile.level}`"
                        @click="$router.push('/vip')"
                    />
                </div>
            </div>
            <!-- 基础信息 -->

            <!-- 资产 -->
            <c-card
                class="my-4"
                data-aos-delay="50"
            >
                <div class="text-title text-sm mb-2.5 flex justify-between text-center">
                    <div class="flex flex-col">
                        <div class="flex items-start flex-col mb-4 text-primary">
                            <div>{{ t('account.balance') }}</div>
                            <c-rate-currency
                                :amount="$spot.usableCash"
                                :disabled="!$isLogin"
                                v-model:currency="currency"
                                v-model:rate="rate"
                            />
                        </div>
                        <div class="flex items-start flex-col text-primary" @click="$router.push('/interest/list')">
                            <div>
                                {{ t('account.interest') }}
                                <!--<van-icon name="arrow"/>-->
                                <van-icon name="play" class="ml-[-5px]"/>
                            </div>
                            <c-amount :amount="$spot.interestCash * rate"/>
                        </div>
                    </div>
                    <van-button
                        type="primary"
                        @click="$router.push('/spot/financial/true')"
                    >
                        {{ t('_fund') }}
                    </van-button>
                </div>

                <div class="bg-controller_bg rounded-lg h-10 flex-between px-4">
                    <span class="text-sm">{{ t('account.freeze') }}</span>
                    <c-amount :amount="$spot.freezeCash * rate"/>
                </div>
            </c-card>
            <!-- 资产 -->

            <c-card
                data-aos-delay="150"
                class="flex-middle mb-4"
                v-if="$globalConfig.service"
            >

                <div class="flex justify-center items-center w-[52px] h-[52px] bg-bg1 rounded-[50%]">
                    <c-icon name="customer1" size="32"/>
                </div>

                <div class="flex-1 ml-4">
                    <div class="mr-auto text-title">
                        {{ t('_exclusive') }}
                    </div>
                </div>

                <van-button
                    type="primary"
                    size="mini"
                    @click="handleToService"
                >
                    {{ t('_service') }}
                </van-button>
            </c-card>

            <!-- 快捷导航 -->
            <c-card data-aos-delay="200">
                <van-grid
                    :border="false"
                    clickable
                >
                    <van-grid-item
                        data-aos="zoom-in"
                        v-for="({ title, icon, to, handler }, i) in nav"
                        :data-aos-delay="i * 50 + 50"
                        :key="icon"
                        :text="title"
                        :to
                        @click="handler"
                    >
                        <template #icon>
                            <div class="flex justify-center items-center w-10 h-10 bg-bg1 rounded-[50%]">
                                <c-icon
                                    prefix="mine"
                                    size="24"
                                    :name="icon"
                                />
                            </div>
                        </template>
                    </van-grid-item>
                </van-grid>
            </c-card>
            <!-- 快捷导航 -->
        </div>
    </van-pull-refresh>

    <FundTransfer v-model="showFundTransfer" @close="closeFundTransfer" />
</template>

<script setup>
import { useMine } from '@/pages/main/mine/composables/index.js'
import bgImage from '/skins/templates/_TEMPLATE_/_THEME_/bg.png'

const {
    router,
    verifyIcon,
    dispatch_refreshProfile,
    $isLogin,
    $profile,
    $profileLoading,
    dispatch_refreshSpot,
    dispatch_resetAccount,
    $spot,
    $spotLoading,
    $globalConfig,
    onDownload,
    currency,
    rate,
    loading,
    _onRefresh,
    onLogoutConfirm,
    handleToService,
    t,
    locale,
    $theme,

    FundTransfer,
    showFundTransfer,
    handleFundTransfer,
    closeFundTransfer
} = useMine()

const nav = computed(() => [ {
    title: 'VIP', icon: 'vip', to: '/vip',
}, {
    title: t('header.mission'), icon: 'mission', to: '/mission',
}, {
    title: t('_financial'), icon: 'financial', handler: () => {
        handleFundTransfer()
    },
}, {
    title: t('header.third'), icon: 'third',  handler: () => {
        sessionStorage.setItem('depositActiveTab', 'third')
        router.push('/deposit/third')
    }
}, {
    title: t('_invite'), icon: 'invite', to: '/invite',
}, {
    title: t('header.q&a'), icon: 'question', to: '/q&a',
}, {
    title: t('header.authentication'), icon: 'authentication', to: '/authentication',
},
    {
        title: t('header.setting'), icon: 'setting', to: '/setting',
    },
    {
        title: t('header.about'), icon: 'information', to: '/about',
    },
    {
        title: t('_exchange'), icon: 'exchange', to: '/exchange',
    }, {
        title: t('auth.logout'), icon: 'logout', handler: onLogoutConfirm,
    } ])

if ($globalConfig.value?.app_download_enabled) {
    const item = {
        title: t('_download'),
        icon: 'download',
        handler: onDownload,
    };

    // 计算倒数第三的位置
    const position = Math.max(nav.value.length - 2, 0);
    nav.value.splice(position, 0, item);
}

const backgroundStyle = computed(() => {
    return {
        backgroundImage: `url("${bgImage}")`,
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'right 0, top',
        backgroundSize: '100%, contain',
        overflow: 'hidden',
    }
})

defineOptions({ name: 'profile' })
</script>

<style scoped>
.van-grid {
    --van-grid-item-text-color: var(--title);
    --van-grid-item-content-padding: 8px;
}
</style>
