<template>
    <c-header :title="t('mine.subordinate.title')" />
    <!--搜索-->
        <div class="flex items-center px-2.5 py-2.5">
            <c-search class="flex-1" v-model="keyWords" :placeholder="t('table.name') + '/'+ t('table.phone')" />
            <van-button class="ml-2.5" size="mini" type="primary" @click="onSearch(keyWords)">
                {{t('search')}}
            </van-button>
        </div>
    <!--表格-->
    <!--表格-->
    <div class="px-2.5 h-[calc(100%-44px-50px-10px)]">
        <c-table
            class="h-full overflow-y-auto overflow-x-hidden bg-white"
            data-aos-anchor="#app"
            data-aos="fade-left"
            data-aos-delay="150"
            :finished="finished"
            :columns="columns"
            @loadMore="onLoadMore"
            :data-source="list"
            v-model:refresh-loading="refreshLoading"
            v-model:load-loading="loadLoading"
            :onRefresh="onRefresh"
        />
    </div>

    <van-popup destroy-on-close v-model:show="showDetail" position="bottom">
        <p class="py-2.5 flex justify-center items-center">{{t('mine.subordinate.detailTitle')}}</p>
        <c-description :label="t('table.name')" class="p-2.5">
            {{rowData?.detail?.memberName || '-'}}
        </c-description>
        <c-description :label="t('table.phone')" class="p-2.5">
            {{rowData?.detail?.maskedMobile || '-'}}
        </c-description>
        <c-description :label="t('mine.subordinate.idCard')" class="p-2.5">
            {{rowData?.detail?.maskedIdCard || '-'}}
        </c-description>
        <c-description :label="t('mine.subordinate.balance')" class="p-2.5">
            {{rowData?.detail?.balance || '-'}}
        </c-description>
        <c-description :label="t('mine.subordinate.margin')" class="p-2.5">
            {{rowData?.detail?.margin || '-'}}
        </c-description>
        <c-description :label="t('mine.subordinate.frozenAmount')" class="p-2.5">
            {{rowData?.detail?.frozenAmount || '-'}}
        </c-description>
    </van-popup>
</template>

<script setup lang="jsx">
import CSearch from '@/components/Search/index.vue'

const keyWords = ref()
const {
    list,                // 分页后的数据
    res: result,         // 接口原始返回
    refreshLoading,
    loadLoading,
    onRefresh,
    onLoadMore,
    finished
} = usePagination({
    url: '/invite/subordinateList',
    params: computed(() => ({
        keyword: keyWords.value,
    })),
})
const rowData = ref({})
const showDetail = ref(false)
const handleMoreClick = async (value) => {
    showDetail.value = true
    rowData.value = value
}
const onSearch = (val) => {
    onRefresh()
}

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            'mine.subordinate.title': '下级代理',
            'mine.subordinate.detailTitle': '下级明细详情',
            'mine.subordinate.idCard': '身份证',
            'mine.subordinate.balance': '余额',
            'mine.subordinate.margin': '保证金',
            'mine.subordinate.frozenAmount': '冻结金额',
            'mine.subordinate.enable': '启用',
            'mine.subordinate.disable': '停用',

            'search': '搜索',
            'table.name': '姓名',
            'table.phone': '手机号',
            'table.totalRebate': '累计返佣',
            'table.activeUsers': '有效用户',
            'table.membershipStatus': '会员状态',
            'table.more': '更多',
            'table.details': '详情',
        },
        [LANGUAGE.zhHK]: {
            'mine.subordinate.title': '下級代理',
            'mine.subordinate.detailTitle': '下級明細詳情',
            'mine.subordinate.idCard': '身份證',
            'mine.subordinate.balance': '餘額',
            'mine.subordinate.margin': '保證金',
            'mine.subordinate.frozenAmount': '凍結金額',
            'mine.subordinate.enable': '啟用',
            'mine.subordinate.disable': '停用',

            'search': '搜尋',
            'table.name': '姓名',
            'table.phone': '手機號',
            'table.totalRebate': '累計返傭',
            'table.activeUsers': '有效用戶',
            'table.membershipStatus': '會員狀態',
            'table.more': '更多',
            'table.details': '詳情',
        },
        [LANGUAGE.enUS]: {
            'mine.subordinate.title': 'Subordinate Agent',
            'mine.subordinate.detailTitle': 'Subordinate Detail',
            'mine.subordinate.idCard': 'ID Card',
            'mine.subordinate.balance': 'Balance',
            'mine.subordinate.margin': 'Margin',
            'mine.subordinate.frozenAmount': 'Frozen Amount',
            'mine.subordinate.enable': 'Enabled',
            'mine.subordinate.disable': 'Disabled',

            'search': 'Search',
            'table.name': 'Name',
            'table.phone': 'Phone Number',
            'table.totalRebate': 'Total Rebate',
            'table.activeUsers': 'Active Users',
            'table.membershipStatus': 'Membership Status',
            'table.more': 'More',
            'table.details': 'Details',
        },
    },
})

const columns = [
    {
        title: t('table.name'), // 姓名
        render: (value) => {
            return <div class="flex flex-col">
                <span class="text-xs!">{value?.detail?.maskedMobile || '-'}</span>
                <span class="text-xs!">{value?.detail?.memberName || '-'}</span>
            </div>
        }
    },
    {
        title: t('table.totalRebate'), // 累计返佣
        align: 'center',
        dataIndex: 'totalRebateFromSubordinate',
        render: (value) => {
            return <span class="text-primary">{value || '-'}</span>
        }
    },
    {
        title: t('table.membershipStatus'), // 会员状态
        align: 'right',
        render: (value) => {
            return <span class="text-xs!">{value?.status ? t('mine.subordinate.enable') : t('mine.subordinate.disable')}</span>
        }
    },
    {
        title: t('table.more'), // 更多
        align: 'right',
        render: (value) =>{
            return <van-button type="primary" onclick={() => handleMoreClick(value)} size="mini">{t('table.details')}</van-button> // 详情
        }
    }
]

defineOptions({
    name: 'subordinate'
})
</script>