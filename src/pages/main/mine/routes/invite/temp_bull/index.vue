<template>
    <div class="h-full flex flex-col invite">
        <c-header :title="t('_title')" bg-color="#FFEAAB" class="shrink-0" />

        <div class="bg-[#FFEAAB]">
            <div class="p-2.5 pt-5 bg-black rounded-tl-[25px] rounded-tr-[25px] mx-2.5">
                <ul class="flex justify-between items-center">
                    <li
                        class="flex flex-col items-center justify-center"
                        v-for="{icon, title, key, handler} in navs"
                        @click="handler"
                        :key="key"
                    >
                        <c-icon :name="icon" size="38"/>
                        <span class="mt-3">{{title}}</span>
                    </li>
                </ul>
                <!--vip卡片-->
                <div
                    class="w-full h-[242px] rounded-2xl mt-5 px-5 py-2.5"
                    :style="cardStyle"
                >
                    <p class="flex justify-start text-xl! font-medium text-[#C44500]">VIP{{res?.currentVipLevel}}</p>

                    <ul class="w-full flex flex-col space-y-3.5 mt-4">
                        <li
                            v-for="row in vipRows"
                            :key="row.key"
                            class="flex w-full"
                        >
                            <div class="text-sm text-[#C44500]" :class="[ !isEN ? 'w-[100px]' : '']">{{ row.label }}</div>
                            <span
                                class="text-[#1D2129] flex-1"
                                :class="[ isEN ? 'inline-flex justify-end' : '']"
                            >{{ row.value }}</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <p class="px-2.5 pt-4 text-[#222] bg-[#F4F7FF]">
            <!--代理等级说明-->
            {{t('agent_level_desc')}}
        </p>

        <div
            style="height: calc(100% - 44px - 313px);"
            class="overflow-y-auto p-[10px] pt-0">
            <c-card class="mt-2.5 bg-white text-sm flex flex-col space-y-2.5" v-for="(v, index) in res?.vipLevelTableList || []" :key="index">
                <div class="flex justify-between">
                    <span class="text-[#8897B8]">{{t('agent_level')}}</span>
                    <span class="text-[#222]">{{`VIP${v?.level}`}}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-[#8897B8]">{{ t('commission_rate')}}</span>
                    <span class="text-[#222]">{{`${v?.rebateRate}%`}}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-[#8897B8]">{{ t('interest_rebate_rate') }}</span>
                    <span class="text-[#222]">{{`${v?.interestRate}%`}}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-[#8897B8]">{{ t('commission_conditions') }}</span>
                    <div class="text-[10px] text-[#222] flex flex-col items-end">
                        <span>{{t('alid_members')}} {{v?.requiredInviteCount}}</span>
                        <span>{{t('cumulative_recharge')}} {{v?.requiredRechargeAmount}}</span>
                    </div>
                </div>
            </c-card>
        </div>
        </div>
</template>

<script setup lang="jsx">
import { useInvite } from '../composables/index.js'
import inviteVipBg from '@skins/templates/temp_bull/yellow/invite_vip_bg.png'

const router = useRouter()

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            'subordinate_list': '下级列表',
            "_title": "邀请返佣",
            "rebate_detail": "返佣明细",
            "generate_link": "生成链接",
            "cumulative_commission": "累计返佣",
            "cumulative_subordinates": "累计下线",
            "trading_commission_rate": "交易返佣比例",
            "interest_commission_rate": "利息返佣比例",
            "agent_level_desc": "代理等级说明",
            "agent_level": "代理等级",
            "commission_conditions": "返佣条件",
            "commission_rate": "返佣比例",
            "interest_rebate_rate": "返息比例",
            "alid_members": '有效人数',
            "cumulative_recharge": '累计充值',
            "cumulative_recharge_amount": "累计充值金额",
        },
        [LANGUAGE.zhHK]: {
            'subordinate_list': '下級列表',
            "_title": "邀請返傭",
            "rebate_detail": "返傭明細",
            "generate_link": "生成鏈接",
            "cumulative_commission": "累計返傭",
            "cumulative_subordinates": "累計下線",
            "trading_commission_rate": "交易返傭比例",
            "interest_commission_rate": "利息返傭比例",
            "agent_level_desc": "代理等級說明",
            "agent_level": "代理等級",
            "commission_conditions": "返傭條件",
            "commission_rate": "返傭比例",
            "interest_rebate_rate": "返息比例",
            "alid_members": '有效人數',
            "cumulative_recharge": '累計充值',
            "cumulative_recharge_amount": "累計充值金額",
        },
        [LANGUAGE.enUS]: {
            'subordinate_list': 'Subordinate List',
            "_title": "Invite Commission",
            "rebate_detail": "Commission Details",
            "generate_link": "Generate Link",
            "cumulative_commission": "Cumulative Commission",
            "cumulative_subordinates": "Cumulative Subordinates",
            "trading_commission_rate": "Trading Commission Rate",
            "interest_commission_rate": "Interest Commission Rate",
            "agent_level_desc": "Agent Level Description",
            "agent_level": "Agent Level",
            "commission_conditions": "Commission Conditions",
            "commission_rate": "Commission Rate",
            "interest_rebate_rate": "Interest Rebate Rate",
            "alid_members": 'Valid Members',
            "cumulative_recharge": 'Cumulative Recharge',
            "cumulative_recharge_amount": "Cumulative Recharge Amount"
        },
    },
})
const isEN = computed(() => {
    return useI18n().locale.value === 'en-US'
})
const vipRows = computed(() => ([
    {
        key: 'totalRebate',
        label: t('cumulative_commission'),
        value: res.value?.totalRebate ?? 0,
    },
    {
        key: 'subs',
        label: t('cumulative_subordinates'),
        value: `${res.value?.totalSubordinates ?? 0}(有效：${res.value?.totalSatisfiedInviteCount ?? 0})`,
    },
    {
        key: 'tradeRate',
        label: t('trading_commission_rate'),
        value: `${res.value?.tradingRebateRate ?? '0'}%`,
    },
    {
        key: 'interestRate',
        label: t('interest_commission_rate'),
        value: `${res.value?.interestRebateRate ?? '0'}%`,
        valueClass: 'flex-1', // 保留你最后一行的伸展效果
    },
    {
        key: 'interestRate',
        label: t('cumulative_recharge_amount'),
        value: `${res.value?.totalRechargeAmount ?? '0'}`,
        valueClass: 'flex-1', // 保留你最后一行的伸展效果
    },
]));


const {
    handleCopy
} = useInvite()
const navs = computed(() => {
    return [
        {
            icon: 'download',
            title: t('subordinate_list'),
            key: 'x_001',
            handler: () => {
                // 跳转到下载列表
                router.push('/subordinate')
            },
        },
        {
            // width: 180,
            icon: 'detail',
            title: t('rebate_detail'),
            key: 'x_002',
            handler: () => {
                // 跳转到返佣明细
                router.push('/rebate')
            },
        },
        {
            icon: 'link',
            title: t('generate_link'),
            key: 'x_003',
            handler: () => {
                handleCopy()
            }
        }]
})

const {res} = useRequest({
    url: '/invite/rebateList'
})

const cardStyle = {
    backgroundImage: `url(${inviteVipBg})`,
    backgroundSize: '100% 242px',
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'center',
}


defineOptions({ name: 'invite' })
</script>

<style scoped>
.invite {
    background: #F4F7FF!important;
}
</style>
