import { useQRCode } from '@vueuse/integrations/useQRCode'

import Divider from '@/components/Divider/index.vue'

export const useInvite = () => {
    const globalStore = useGlobalStore()
    const {$globalConfig} = storeToRefs(globalStore)
    const show = ref(false)

    // 后台配置的邀请链接
    const uri = $globalConfig.value?.invite_code_link

    const href = shallowRef('')

    const { res } = useRequest({
        url: '/invite/detail',
        initialValues: {
            commissionRate: 0,
            interestRate: 0,
            inviteCode: '',
            totalCommission: 0,
        },
        onSuccess: (res) => {
            href.value = window.location.origin + '/#/auth/register?inviteCode=' + res.inviteCode
        },
    })

    // 后台配置的优先
    const newUri = computed(() => {
        return uri ? `${uri}?inviteCode=${res.value.inviteCode}` : href.value
    })

    const { copy } = useClipboard({ source: newUri })

    const onCopy = (content) => {
        copy(content)
        showSuccessToast(t('operation.copy_successfully'))
    }

    const handleCopy = () => {
        copy(newUri.value)
        showSuccessToast(t('operation.copy_successfully'))
    }

    const qrcode = useQRCode(newUri)

    const { t } = useI18n({
        useScope: 'global',
        messages: {
            [LANGUAGE.zhCN]: {
                _title: '邀请返佣',
                _subhead: '邀请好友 赚取佣金',
                _description: '进行股票，股指交易佣金最高达90%',
                _vip: '等级越高，赚取佣金比例更高还有机会获得锦鲤奖！',
                _qrcode: '二维码',
                _referrer: '推广码',
                _link: '生成邀请链接',
                _commission: '我的佣金',
            },
            [LANGUAGE.zhHK]: {
                _title: '邀请返佣',
                _subhead: '邀请好友 赚取佣金',
                _description: '进行股票，股指交易佣金最高达90%',
                _vip: '等级越高，赚取佣金比例更高还有机会获得锦鲤奖！',
                _qrcode: '二维码',
                _referrer: '推广码',
                _link: '生成邀请链接',
                _commission: '我的佣金',
            },
            [LANGUAGE.enUS]: {
                _title: 'Referral Rewards',
                _subhead: 'Invite friends, earn commissions',
                _description: 'Commissions up to 90% for stock and index trading',
                _vip: 'Upgrade VIP level to get higher commission ratio!',
                _qrcode: 'QR Code',
                _referrer: 'Referral Code',
                _link: 'Generate Invitation Link',
                _commission: 'My Commission',
            },
        },
    })

    return {
        useQRCode,
        Divider,
        show,
        href,
        res,
        copy,
        onCopy,
        handleCopy,
        qrcode,
        t,
    }
}

// http://localhost:9999/#/auth/register?inviteCode=M9NGABY8