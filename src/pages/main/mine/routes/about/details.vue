<template>
    <c-header :title="detail.title"/>

    <div class="with-header-container">

        <div class="flex flex-col overflow-y-auto">
            <div
                class="flex-1 py-4 text-paragraph text-justify!"
                v-html="htmlStr"
                :style="detail.sealImageUrl ? {
                      '--seal': `url(${detail.sealImageUrl})`
                      } : {}"
            />
        </div>
    </div>
</template>

<script setup>
import _ from 'lodash'
const { $protocol } = storeToRefs(useProtocolStore())
import { $theme } from '@/store/index.js'
const { params: { id } } = useRoute()

const detail = computed(() => _.find($protocol.value, { id: +id }) ?? {
    id: 0, title: '', content: '',
})

// -------- 核心：确保暗色可读的替换 ----------
const DARK_BG = { r: 18, g: 18, b: 18 } // 约 #121212
const MIN_RATIO = 4.5

const toRgb = (s) => {
    s = s.trim().toLowerCase()
    if (s.startsWith('#')) {
        const hex = s.slice(1)
        const n = hex.length === 3
            ? hex.split('').map(ch => parseInt(ch + ch, 16))
            : [hex.slice(0,2),hex.slice(2,4),hex.slice(4,6)].map(h=>parseInt(h,16))
        return { r: n[0], g: n[1], b: n[2], a: 1 }
    }
    if (s.startsWith('rgb')) {
        const nums = s.match(/[\d.]+/g)?.map(Number) ?? []
        return { r: nums[0] ?? 0, g: nums[1] ?? 0, b: nums[2] ?? 0, a: (nums[3] ?? 1) }
    }
    if (s === 'black') return { r:0,g:0,b:0,a:1 }
    if (s === 'white') return { r:255,g:255,b:255,a:1 }
    return null
}
const relLum = ({r,g,b}) => {
    const f = (c)=>{ c/=255; return c<=0.03928? c/12.92 : Math.pow((c+0.055)/1.055,2.4) }
    const [R,G,B] = [f(r),f(g),f(b)]
    return 0.2126*R + 0.7152*G + 0.0722*B
}
const contrast = (fg, bg) => {
    const L1 = relLum(fg), L2 = relLum(bg)
    const hi = Math.max(L1,L2), lo = Math.min(L1,L2)
    return (hi + 0.05) / (lo + 0.05)
}

function ensureReadableColors(html, isDark) {
    if (!isDark) return html
    // 把颜色写成统一形式再判断：匹配 rgb()/rgba()/十六进制/black
    return html.replace(
        /color\s*:\s*(#[0-9a-fA-F]{3,6}|rgba?\([^)]+\)|black|white)\s*;?/gi,
        (m, colorStr) => {
            const rgb = toRgb(colorStr)
            if (!rgb) return m
            // 透明色当作不指定颜色
            if (rgb.a !== undefined && rgb.a < 0.05) return ''
            // 对比度不够则换成高对比白色
            const ratio = contrast(rgb, DARK_BG)
            if (ratio < MIN_RATIO) {
                return 'color: rgba(255,255,255,0.92);'
            }
            return m // 可读则保留原色（比如红色标题）
        }
    )
}

// 你原来的变量替换基础上，最后调用 ensureReadableColors
function replaceVars(template) {
    if (!template) return ''
    let str = template

    // ……（保留你现有的变量替换）……
    str = str.replace(/{{party_b}}/g, "")
    str = str.replace(/{{party_mobile}}/g, "")
    str = str.replace(/{{contract_start_date}}/g, " 年 月 日")
    str = str.replace(/{{contract_end_date}}/g, " 年 月 日")
    str = str.replace(/{{original_a_capital_1}}/g, "")
    str = str.replace(/{{original_a_capital_2}}/g, "")
    str = str.replace(/{{original_b_capital_1}}/g, "")
    str = str.replace(/{{original_b_capital_2}}/g, "")
    str = str.replace(/{{interest_rate}}/g, "")
    str = str.replace(/{{interest_amount}}/g, "")

    // 最后一步：暗色确保可读
    str = ensureReadableColors(str, $theme?.value === 'dark')
    return str
}

// 用 computed，主题或内容变化时自动更新
const htmlStr = computed(() => replaceVars(detail.value.content))

defineOptions({ name: 'about-details' })
</script>

<style scoped>
.text-paragraph {
    position: relative;
}

.text-paragraph::after {
    content: "";
    position: absolute;
    left: 50px;
    bottom: 60px;
    width: 120px;
    height: 120px;
    background: var(--seal) no-repeat center center;
    background-size: contain;
    pointer-events: none;
    opacity: 0.7;
}
</style>