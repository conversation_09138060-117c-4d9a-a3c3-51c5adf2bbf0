<template>
    <c-header :title="t('mine.subordinate.title')" />
    <!--搜索-->
        <div class="flex items-center px-2.5 py-2.5">
            <c-search class="flex-1" v-model="keyWords" :placeholder="t('table.name') + '/'+ t('table.phone')" />
            <van-button class="ml-2.5" size="mini" type="primary" @click="onSearch(keyWords)">
                {{t('search')}}
            </van-button>
        </div>

    <!--表格-->
   <div class="px-2.5 h-[calc(100%-44px-50px-10px)]">
       <c-table
           class="h-full overflow-y-auto overflow-x-hidden bg-white"
           data-aos-anchor="#app"
           data-aos="fade-left"
           data-aos-delay="150"
           :finished="finished"
           :columns="columns"
           @loadMore="onLoadMore"
           :data-source="list"
           v-model:refresh-loading="refreshLoading"
           v-model:load-loading="loadLoading"
           :onRefresh="onRefresh"
       />
   </div>

    <van-popup destroy-on-close v-model:show="showDetail" position="bottom">
        <p class="py-2.5 flex justify-center items-center">{{t('mine.subordinate.title')}}</p>
        <c-description :label="t('table.name')" class="p-2.5">
            {{rowData?.name || '-'}}
        </c-description>
        <c-description :label="t('table.phone')" class="p-2.5">
            {{rowData?.mobile || '-'}}
        </c-description>
        <c-description :label="t('mine.subordinate.contractName')" class="p-2.5">
            <div class="truncate">
                {{ utils_contract_name({ marketType: rowData?.marketType, type: rowData?.contractType, periodType: rowData?.periodType, multiple: rowData?.multiple, id: rowData?.contractId }) || '-' }}
            </div>
        </c-description>
        <c-description :label="t('table.refundAmount')" class="p-2.5">
            {{rowData?.refundAmount || '-'}}
        </c-description>
        <c-description :label="t('table.rebateAmount')" class="p-2.5">
            {{rowData?.rebateAmount || '-'}}
        </c-description>
        <c-description :label="t('mine.subordinate.contractNumber')" class="p-2.5">
            {{rowData?.contractNumber || '-'}}
        </c-description>
        <c-description :label="t('mine.subordinate.deductionTime')" class="p-2.5">
            {{rowData?.createTime || '-'}}
        </c-description>
    </van-popup>

</template>

<script setup lang="jsx">
import CSearch from '@/components/Search/index.vue'
import { utils_contract_name } from '@/utils/index.js'

const keyWords = ref()

const {
    list,                // 分页后的数据
    res: result,         // 接口原始返回
    refreshLoading,
    loadLoading,
    onRefresh,
    onLoadMore,
    finished
} = usePagination({
    url: '/invite/refundCommission',
    params: computed(() => ({
        keyWord: keyWords.value,  // 👈 把搜索关键字传给接口
    })),
})

const showDetail = ref(false)

const onSearch = (val) => {
    onRefresh()
}
const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            'mine.subordinate.title': '返佣明细',
            'mine.subordinate.detailTitle': '返佣明细详情',
            'mine.subordinate.contractName': '合约名称',
            'mine.subordinate.contractNumber': '合约单号',
            'mine.subordinate.deductionTime': '扣息时间',

            'search': '搜索',
            'table.name': '姓名',
            'table.phone': '手机号',
            'table.level': '等级',
            'table.rebateAmount': '返佣金额',
            'table.refundAmount': '返息金额',
            'table.more': '更多',
            'table.detail': '详情',
        },
        [LANGUAGE.zhHK]: {
            'mine.subordinate.title': '返傭明細',
            'mine.subordinate.detailTitle': '返傭明細詳情',
            'mine.subordinate.contractName': '合約名稱',
            'mine.subordinate.contractNumber': '合約單號',
            'mine.subordinate.deductionTime': '扣息時間',

            'search': '搜尋',
            'table.name': '姓名',
            'table.phone': '手機號',
            'table.level': '等級',
            'table.rebateAmount': '返傭金額',
            'table.refundAmount': '返息金額',
            'table.more': '更多',
            'table.detail': '詳情',
        },
        [LANGUAGE.enUS]: {
            'mine.subordinate.title': 'Rebate Details',
            'mine.subordinate.detailTitle': 'Rebate Detail',
            'mine.subordinate.contractName': 'Contract Name',
            'mine.subordinate.contractNumber': 'Contract Number',
            'mine.subordinate.deductionTime': 'Deduction Time',

            'search': 'Search',
            'table.name': 'Name',
            'table.phone': 'Phone',
            'table.level': 'Level',
            'table.rebateAmount': 'Rebate Amount',
            'table.refundAmount': 'Refund Amount',
            'table.more': 'More',
            'table.detail': 'Details',
        },
    },
})

const rowData = ref({})

const handleMoreClick = async (value) => {
    showDetail.value = true
    rowData.value = value
}

const columns = [
    {
        title: t('table.name'),
        render: (val) => {
            return <div class="flex flex-col">
                <span class="text-xs!">{val?.mobile || '-'}</span>
                <span class="text-xs!">{val?.name || '-'}</span>
            </div>
        }
    },
    // 返佣金额
    {
        title: t('table.rebateAmount'),
        align: 'center',
        dataIndex: 'rebateAmount',
        render: (val) => {
            return <span class="text-xs! text-primary">{val || '0'}</span>
        }
    },
    // 返息金额
    {
        title: t('table.refundAmount'),
        dataIndex: 'refundAmount',
        align: 'center',
        render: (val) => {
            return <span>{val || '-'}</span>
        }
    },
    {
        title: t('table.more'),
        align: 'right',
        render: (value) => {
            return <van-button
                type="primary"
                size="mini"
                onclick={() => handleMoreClick(value)}
            >
                {t('table.detail')}
            </van-button>
        }
    }
]

defineOptions({
    name: "rebate"
})
</script>