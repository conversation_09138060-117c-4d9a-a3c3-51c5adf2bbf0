import verification_zhCN from '@skins/templates/_TEMPLATE_/_THEME_/<EMAIL>'
import verification_zhHK from '@skins/templates/_TEMPLATE_/_THEME_/<EMAIL>'
import verification_enUS from '@skins/templates/_TEMPLATE_/_THEME_/<EMAIL>'
import FundTransfer from '@/components/FundTransfers/index.vue'
import CouponDialog from '@/pages/main/mine/components/CouponDialog.vue'

import _ from 'lodash'
import i18n from '@/i18n/index.js'
import { $theme } from '@/store/global.js'

export const useMine = () => {
    const router = useRouter()
    const showCouponModal = ref(false)
    const verifyIcon = {
        [LANGUAGE.zhCN]: verification_zhCN, [LANGUAGE.zhHK]: verification_zhHK, [LANGUAGE.enUS]: verification_enUS,
    }
    const showFundTransfer = ref(false)

    const handleFundTransfer = () => {
        showFundTransfer.value = true
    }
    const closeFundTransfer = () => {
        showFundTransfer.value = false
    }

    const {list} = usePagination({
        url: 'asset/detail/page'
    })

    const profileStore = useProfileStore(), { dispatch_refreshProfile } = profileStore, {
        $isLogin,
        $profile,
        $profileLoading,
    } = storeToRefs(profileStore), accountStore = useAccountStore(), {
        dispatch_refreshSpot,
        dispatch_resetAccount,
    } = accountStore, { $spot, $spotLoading } = storeToRefs(accountStore), { $globalConfig } = storeToRefs(useGlobalStore())

    const onDownload = useDownload()

    const [ currency, rate ] = useRate()

    const loading = computed(() => $spotLoading.value || $profileLoading.value)

    const _onRefresh = async () => {
        await Promise.all([ dispatch_refreshSpot(), dispatch_refreshProfile() ])
    }

    const handleToService = _.debounce(async () => {
        const { pImAccount, pNickname } = await api_get({
            url: '/tc/getUserKefu',
        })

        if (pImAccount && pNickname) {
            router.push(`/service/${pImAccount}`)
        } else {
            showDialog({
                title: i18n.global.t('server.tip'),
            })
        }
    }, 500, { leading: true })

    const { onLogoutConfirm } = useLogout()

    const { t, locale } = useI18n({
        useScope: 'global', messages: {
            [LANGUAGE.zhCN]: {
                _exclusive: '专属客服通道',
                _entry: '立即进入',
                _financial: '入金/出金',
                _invite: '邀请返佣',
                _newbie: '新手专区',
                _authentication: '实名认证',
                _download: 'App 下载',
                _exchange: '汇率查询',
                _service: '立即咨询',
                _fund: '资金记录',
            }, [LANGUAGE.zhHK]: {
                _exclusive: "專屬客服通道",
                _entry: "立即進入",
                _financial: "入金/出金",
                _invite: "邀請返傭",
                _newbie: "新手專區",
                _authentication: "實名認證",
                _download: "App 下載",
                _exchange: "匯率查詢",
                _service: "立即諮詢",
                _fund: "資金記錄"
            }, [LANGUAGE.enUS]: {
                _exclusive: 'Exclusive Service',
                _entry: 'Entry',
                _financial: 'Financial',
                _invite: 'Invite',
                _newbie: 'Newbie',
                _authentication: 'AuthN',
                _download: 'Download',
                _exchange: 'Exchange',
                _service: 'Consult now',
                _fund: 'Fund Record',
            },
        },
    })

    const handleInterest = () => {
        showCouponModal.value = true
    }

    watch(() => $isLogin, async (bool) => {
        if (!bool.value) {
            dispatch_resetAccount()
        } else {
            // 登录后刷新数据
            await _onRefresh()
        }
    }, { immediate: true })

    return {
        router,
        verifyIcon,
        dispatch_refreshProfile,
        $isLogin,
        $profile,
        $profileLoading,
        dispatch_refreshSpot,
        dispatch_resetAccount,
        $spot,
        $spotLoading,
        $globalConfig,
        onDownload,
        currency,
        rate,
        loading,
        handleInterest,
        CouponDialog,
        showCouponModal,
        _onRefresh,
        onLogoutConfirm,
        handleToService,
        t,
        locale,
        $theme,
        FundTransfer,
        showFundTransfer,
        handleFundTransfer,
        closeFundTransfer
    }
}
