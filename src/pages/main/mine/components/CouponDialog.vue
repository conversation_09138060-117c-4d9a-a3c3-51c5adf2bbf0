<template>
    <van-popup
        :show="show"
        round
        teleport="body"
        close-on-click-overlay
        @update:show="$emit('update:show', $event)"
    >
        <div class="w-[88vw] max-w-[420px]">
            <!-- 头部 -->
            <div class="relative py-4 px-5 text-center font-semibold text-[18px]">
                <span>{{ title || '利息券' }}</span>
                <van-icon name="cross" class="absolute right-[14px] top-3" @click="$emit('update:show', false)" />
            </div>
            <van-divider class="!m-0" />

            <van-tabs v-model:active="active" swipeable @change="onTabChange">
                <template #nav-right>
                   <div  class="right-[14px] top-3 w-[180px]">
                       <van-button
                           type="primary"
                           size="mini"
                           @click="$router.push('/interest/list')"
                       >
                           {{t('account.interest')}}
                       </van-button>
                   </div>
                </template>
                <!-- 未使用(status=1) -->
                <van-tab name="canUse" :title="t('unusedTab')">
                    <div class="max-h-[60vh] overflow-auto pt-1 px-3 pb-4">
                        <van-pull-refresh v-model="refreshLoadingUnused" @refresh="handleRefreshUnused">
                            <van-list
                                v-model:loading="loadLoadingUnused"
                                :finished="finishedUnused"
                                :finished-text="t('finishedText')"
                                :immediate-check="false"
                                @load="handleLoadMoreUnused"
                            >
                                <template v-if="listUnused.length">
                                    <van-cell-group inset>
                                        <van-cell
                                            v-for="item in listUnused"
                                            :key="item.id"
                                            clickable
                                            :title="`利息券 ￥${item.amount}`"
                                            :label="`有效期：${item.validStartTime ?? '-'}—${item.validEndTime ?? '-'}`"
                                            @click="$emit('select', item)"
                                        >
                                            <template #value>
                                                <span class="text-sm text-[#11a66a]">{{ t('unused') }}</span>
                                            </template>
                                        </van-cell>
                                    </van-cell-group>
                                </template>
                                <van-empty
                                    v-else-if="!loadLoadingUnused && !refreshLoadingUnused && finishedUnused"
                                    :description="t('emptyUnused')"
                                />
                            </van-list>
                        </van-pull-refresh>
                    </div>
                </van-tab>

                <!-- 已使用/过期(status=5) -->
                <van-tab name="canNotUse" :title="t('usedTab')">
                    <div class="max-h-[60vh] overflow-auto pt-1 px-3 pb-4">
                        <van-pull-refresh v-model="refreshLoadingUsed" @refresh="handleRefreshUsed">
                            <van-list
                                v-model:loading="loadLoadingUsed"
                                :finished="finishedUsed"
                                :finished-text="t('finishedText')"
                                :immediate-check="false"
                                @load="handleLoadMoreUsed"
                            >
                                <template v-if="listUsed.length">
                                    <van-cell-group inset>
                                        <van-cell
                                            v-for="item in listUsed"
                                            :key="item.id"
                                            :title="`利息券 ￥${item.amount}`"
                                            :label="`有效期：${item.validStartTime ?? '-'}—${item.validEndTime ?? '-'}`"
                                        >
                                            <template #value>
                                                <span class="text-sm text-[#9aa4b2]">{{ usedStatusText(item.status) }}</span>
                                            </template>
                                        </van-cell>
                                    </van-cell-group>
                                </template>
                                <van-empty
                                    v-else-if="!loadLoadingUsed && !refreshLoadingUsed && finishedUsed"
                                    :description="t('emptyUsed')"
                                />
                            </van-list>
                        </van-pull-refresh>
                    </div>
                </van-tab>
            </van-tabs>
        </div>
    </van-popup>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { usePagination, FETCH_METHOD } from '@/apis/fetch.js' // ← 你的 hooks 路径

const props = defineProps({
    show: Boolean,
    title: String,
    // 接口与过滤
    apiUrl: { type: String, default: '/asset/detail/page' }, // 改为你的实际地址
    fromType: [Number, String],  // 可选：来源筛选
    pageSize: { type: Number, default: 20 },
})
defineEmits(['update:show', 'select'])

const active = ref('canUse')

const { t } = useI18n({
    useScope: 'local',
    messages: {
        'zh-CN': {
            unusedTab: '未使用', usedTab: '已使用/过期',
            unused: '未使用', usedOrExpired: '已使用/过期',
            partial: '部分使用', usedUp: '已用完', expired: '已过期',
            emptyUnused: '暂无未使用', emptyUsed: '暂无记录',
            finishedText: '没有更多了',
        },
        'zh-HK': {
            unusedTab: '未使用', usedTab: '已使用/過期',
            unused: '未使用', usedOrExpired: '已使用/過期',
            partial: '部分使用', usedUp: '已用完', expired: '已過期',
            emptyUnused: '暫無未使用', emptyUsed: '暫無記錄',
            finishedText: '沒有更多了',
        },
        'en-US': {
            unusedTab: 'Unused', usedTab: 'Used/Expired',
            unused: 'Unused',
            usedOrExpired: 'Used/Expired',
            partial: 'Partially Used', usedUp: 'Used Up', expired: 'Expired',
            emptyUnused: 'No unused coupons', emptyUsed: 'No records',
            finishedText: 'No more',
        },
    },
})

// === 未使用(status=1) ===
const {
    list: listUnused,
    loadLoading: loadLoadingUnused,
    refreshLoading: refreshLoadingUnused,
    finished: finishedUnused,
    onLoadMore: onLoadMoreUnused,
    onRefresh: onRefreshUnused,
    run: runUnused,
} = usePagination(
    {
        url: props.apiUrl,
        method: FETCH_METHOD.GET,
        manual: true,
        params: computed(() => ({
            status: 1,
            fromType: props.fromType,
        })),
    },
    {
        paginationKeys: { current: 'pageNumber', pageSize: 'pageSize' },
        paginationOptions: { pageSize: props.pageSize },
    }
)

// === 已使用/过期(status=5) ===
const {
    list: listUsed,
    loadLoading: loadLoadingUsed,
    refreshLoading: refreshLoadingUsed,
    finished: finishedUsed,
    onLoadMore: onLoadMoreUsed,
    onRefresh: onRefreshUsed,
    run: runUsed,
} = usePagination(
    {
        url: props.apiUrl,
        method: FETCH_METHOD.GET,
        manual: true,
        params: computed(() => ({
            status: 5,
            fromType: props.fromType,
        })),
    },
    {
        paginationKeys: { current: 'pageNumber', pageSize: 'pageSize' },
        paginationOptions: { pageSize: props.pageSize },
    }
)

// 懒加载：弹出时/切 tab 时再拉首屏
watch(() => props.show, (v) => {
    if (!v) return
    if (active.value === 'canUse' && !listUnused.value.length) runUnused()
    if (active.value === "canNotUse" && !listUsed.value.length) runUsed()
})

const onTabChange = (index) => {
    if (index === 0 && !listUnused.value.length) runUnused()
    if (index === 1 && !listUsed.value.length) runUsed()
}

// 触底 / 下拉
const handleLoadMoreUnused = async () => { await onLoadMoreUnused() }
const handleLoadMoreUsed   = async () => { await onLoadMoreUsed() }
const handleRefreshUnused  = async () => { await onRefreshUnused() }
const handleRefreshUsed    = async () => { await onRefreshUsed() }

// 状态文案（接口文档：1=未使用 2=部分使用 3=已用完 4=已过期）
const usedStatusText = (s) => {
    if (s === 2) return t('partial')
    if (s === 3) return t('usedUp')
    if (s === 4) return t('expired')
    return t('usedOrExpired')
}
</script>
