<template>
    <c-header :title="title || t('contract.details')">
        <template #right>
           <div @click="onShowProtocol" class="text-xs pl-2.5 py-2.5">{{t('contract.trading_agreement')}}</div>
        </template>
    </c-header>

    <div class="with-header-container">
        <c-card
            data-aos="fade-left"
            data-aos-delay="50"
        >
            <c-description-group :items="baseInfo"/>
        </c-card>

        <c-card
            class="mt-4"
            data-aos="fade-left"
            data-aos-delay="100"
        >
            <c-description-group :items="details">
                <template #template="{ value }">
                    <c-amount
                        v-if=" typeof value === 'number'"
                        :amount="value"
                    />
                    <span v-else>
                       {{ value }}
                    </span>
                </template>
            </c-description-group>
        </c-card>

        <slot/>
    </div>
    <ProtocolPopup
     :type="4"
     :capital_a="capital_a"
     :capital_b ="contractDetails.initCash"
     :rate="contractDetails?.interestRate"
     :rateAmount="contractDetails?.interestAmount"
     :start_date="dayjs(contractDetails?.openTime).format('YYYY年MM月DD日')"
     :end_date="dayjs(contractDetails?.expireTime).format('YYYY年MM月DD日')"
    />
</template>

<script setup lang="jsx">
import _ from 'lodash'
import currencyJs from 'currency.js'
import dayjs from 'dayjs'
import { useContractStore } from '@/store/contract.js'

defineProps({
    title: String,
})

const {contractDetail: contractDetails} = storeToRefs(useContractStore())

const capital_a = computed(() => {
    // const {totalPower, initCash, expendAmount, coverLossAmount} = contractDetails.value
    // return (+totalPower - +initCash - +expendAmount - +coverLossAmount)
    return contractDetails.value?.totalFinance ?? 0.00

})

const baseInfo = computed(() => {
    const { periodType, openTime, expireTime, multiple } = contractDetails.value
    return [
        { label: t('contract.market'), value: utils_contract_name(contractDetails.value) },
        { label: t('contract.period'), value: t(`contract.period_${periodType}`) },
        {
            label: t('form.date'),
            value: _formatDate(openTime, expireTime),
        },
        { label: t('contract.multiple'), value: t('contract.multiples', [ multiple ]) },
    ]
})

const _formatDate = (openTime, expireTime) => {
    if (!openTime && !expireTime) return '-'
    else if (openTime && expireTime) return `${utils_time(openTime, TIME_FORMAT.YMD)} - ${utils_time(expireTime, TIME_FORMAT.YMD)}`
}

const details = computed(() => {
    const {
        type,
        totalPower,
        initCash,
        totalFinance,
        giveAmount,
        warnRemindAmount,
        closeRemindAmount,
        interestAmount,
        positionAmount,
        expendAmount,
        coverLossAmount,
        accountWinAmount,
        withdrawAmount,
        freezePower,
        // negativeAmount,
        contractAssetAmount,
        gapWarnRemindAmount,
        gapCloseRemindAmount,
        interestRate,
        winRate,
        isAutoRenew,
        receivableInterest,
    } = contractDetails.value

    //

    const base = [
        // 总操盘资金
        { label: t('contract.total_assets'), value: totalPower },
        // 起始保证金
        { label: t('contract.init_principal'), value: initCash },
        // 亏损警戒线
        { label: t('contract.warning'), value: warnRemindAmount },
        // 亏损平仓线
        { label: t('contract.close'), value: closeRemindAmount },
        // 利息利率
        { label: t('account.interestRate'), value: `${currencyJs(interestRate)}%` },
        // 累计利息
        { label: t('contract.rate_amount'), value: receivableInterest },
        // 市值
        { label: t('stock.market_value'), value: positionAmount },
        // 扩大合约
        { label: t('contract.expand'), value: expendAmount },
        // 追加合约
        { label: t('contract.replenish'), value: coverLossAmount },
        // 持仓盈亏
        { label: () => {
                return <span>
                    {t('account.earnings')}
                    {/*TODO 图标待替换*/}
                    {/*<van-icon name="question" />*/}
                </span>
        }, value: accountWinAmount },
        // 盈亏率
        { label: t('account.lossRate'), value: `${currencyJs(winRate)}%` },
        // 提盈金额
        { label: t('financial.withdrawal.amount'), value: withdrawAmount },
        // 冻结金额
        { label: t('account.freeze'), value: freezePower },
        // 穿仓金额
        // { label: t('account.amount_of_loss'), value: negativeAmount },
        // 合约净资产
        { label: t('account.netAsset'), value: contractAssetAmount },
        // 距离预警线金额
        { label: t('account.distanceToWarningLine'), value: gapWarnRemindAmount },
        // 距离强平线金额
        { label: t('account.distanceToLiquidationLine'), value: gapCloseRemindAmount },
        // 合约状态
        {
            label: t('account.contractStatus'), value: isAutoRenew ? t('account.autoRenewal') : t('account.settleOnExpiration'),
        },
    ]

    // 彩金合约
    if (type === 3) {
        base.splice(2, 0, {
            label: t('contract.gift_amount'),
            value: giveAmount,
        })
    }

    // 体验合约
    if (type === 2) {
        const _details = _.at(base, [ 0, 2, 3, 9, 10, 13, 14, 15, 16 ])
        _details.splice(1, 0, {
            label: t('contract.gift_amount'),
            // value: totalFinance,
            value: giveAmount,
        })

        return _details
    }

    return base
})

const { t } = useI18n()

const { onShowProtocol, ProtocolPopup } = useProtocolPopup(4)
defineOptions({ name: 'contract-details' })
</script>

<style scoped>

</style>
