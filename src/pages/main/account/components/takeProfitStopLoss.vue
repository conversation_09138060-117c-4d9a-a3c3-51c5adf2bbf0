<template>
    <slot name="trigger" :onOpen="handleOpen"/>

    <van-action-sheet teleport="body" v-model:show="show">
        <div class="px-4 py-5">
            <div class="mb-2.5">止盈止损</div>

            <div class="flex justify-between mt-2.5">
                <span class="text-text">期货</span>
                <div>
                    <span>{{ data?.symbolName }}</span>
                    <!--tradeType 1 做多 2 做空-->
                    &nbsp;
                    <span v-if="+data?.tradeType === 1" class="text-raise">多</span>
                    <span v-else class="text-fall">空</span>
                </div>
            </div>

            <div class="flex justify-between mt-2.5">
                <span class="text-text">开仓价格(CNY)</span>
                <c-amount color="text-primary" :amount="data?.buyAvgPrice ?? 0"/>
            </div>

            <div class="flex justify-between mt-2.5">
                <span class="text-text">现价(CNY)</span>
                <c-amount color="text-primary" :amount="data?.stockPrice ?? 0"/>
            </div>

            <!--止盈-->
            <div class="mt-2.5">
                <span class="mb-2.5 inline-block">止盈(CNY)</span>
                <!--type="number" min="10" max-->
                <c-input
                    placeholder="止盈价格"
                    v-model="formState.takeProfitValue"
                >
                    <template #suffix>
                        <span class="text-text">CNY</span>
                    </template>
                </c-input>
                <div class="text-xs mt-1.25 text-justify flex flex-col">
                    <span>当现价触达{{ formState.takeProfitValue }}时，将会触发市价止盈委托卖出目前部位。</span>
                    <span>预期盈利为 <span :class="utils_amount_color(expectedProfit)">{{
                            expectedProfit
                        }}</span>。</span>
                </div>
            </div>

            <!--止损-->
            <div class="mt-2.5">
                <span class="mb-2.5 inline-block">止损(CNY)</span>
                <!--type="number" min="10" max-->
                <c-input
                    placeholder="止损价格"
                    v-model="formState.stopLossValue"
                >
                    <template #suffix>
                        <span class="text-text">CNY</span>
                    </template>
                </c-input>
                <div class="text-xs mt-1.25 text-justify flex flex-col">
                    <span>当现价触达{{ formState.stopLossValue }}时，将会触发市价止盈委托卖出目前部位。</span>
                    <span>预期盈亏为
                        <span :class="utils_amount_color(expectedLoss)">
                        {{ expectedLoss }}
                        </span>。</span>
                </div>
            </div>
            <!--确认按钮-->
            <div class="w-full my-5">
                <van-button @click="handleSubmit" class="w-full" type="primary" size="small">确定</van-button>
            </div>
        </div>
    </van-action-sheet>
</template>

<script setup>
import { utils_amount_color } from '@/utils/index.js'
import currencyJs from 'currency.js'

const { t } = useI18n({
    useScope: 'global',
    messages: {
        [LANGUAGE.zhCN]: {
            _successfully: '设置成功',
        },
        [LANGUAGE.zhHK]: {
            _successfully: '設定成功',
        },
        [LANGUAGE.enUS]: {
            _successfully: 'Setting successful',
        },
    },
})
const show = ref(false)
const data = ref({})
const formState = reactive({
    takeProfitValue: '',
    stopLossValue: '',
})


const handleOpen = (v) => {
    data.value = v
    const { res } = useRequest({
        url: `/position/get/${v.id}`,
        initialValues: {
            cancelTime: '',
            currency: '',
            dealNum: 0,
            dealPrice: 0,
            dealTime: '',
            direction: 0,
            id: 0,
            market: '',
            priceType: 0,
            securityType: '',
            status: 0,
            stockPrice: 0,
            symbol: '',
            symbolName: '',
            tradeNum: 0,
            tradePrice: 0,
            tradeTime: '',
            tradeType: 0,
            transactionAmount: 0,
            type: 0,
            costPrice: 0,
            winAmount: 0,
            tradeFee: 0,
            contractId: 0,
            contractType: 0,
            multiple: 0,
            periodType: 0,
            tradeRate: 0,
        },
    })
    watchEffect(() => {
        formState.takeProfitValue = res?.value?.takeProfitValue ?? ''
        formState.stopLossValue = res.value?.stopLossValue
    })
    show.value = true

}

// 止盈预期
// +data?.tradeType === 1 ? '做多' : '做空'
const expectedProfit = computed(() => {
    const tradeType = +data.value?.tradeType
    const takeProfit = formState.takeProfitValue

    if (!takeProfit) return '-'

    const buyAvgPrice = currencyJs(data.value?.buyAvgPrice || 0)
    const takeProfitPrice = currencyJs(takeProfit)
    const restNum = currencyJs(data.value?.restNum || 0)
    const tradeUnit = currencyJs(data.value?.tradeUnit || 0)

    let profit

    if (tradeType === 1) {
        // 做多
        profit = takeProfitPrice.subtract(buyAvgPrice)
    } else if (tradeType === 2) {
        // 做空
        profit = buyAvgPrice.subtract(takeProfitPrice)
    } else {
        return '-'
    }

    if (profit.value <= 0) return '-'

    return profit.multiply(restNum).multiply(tradeUnit).value.toFixed(2)
})

// 止损预期
const expectedLoss = computed(() => {
    const tradeType = +data.value?.tradeType
    const stopLoss = formState.stopLossValue

    if (!stopLoss) return '-'

    const buyAvgPrice = currencyJs(data.value?.buyAvgPrice || 0)
    const stopLossPrice = currencyJs(stopLoss)
    const restNum = currencyJs(data.value?.restNum || 0)
    const tradeUnit = currencyJs(data.value?.tradeUnit || 0)

    let loss

    if (tradeType === 1) {
        // 做多
        loss = stopLossPrice.subtract(buyAvgPrice)
    } else if (tradeType === 2) {
        // 做空
        loss = buyAvgPrice.subtract(stopLossPrice)
    } else {
        return '-'
    }

    if (loss.value >= 0) return '-'

    return loss.multiply(restNum).multiply(tradeUnit).value.toFixed(2)
})


// 止盈
const expectedProfitValue = computed(() => {
    // data.tradeType  1 做多 涨了赚钱 2 做空 跌了赚钱
    if (+data.value?.tradeType === 1) {
        return (formState?.takeProfitValue - data.value?.buyAvgPrice) * data.value?.bugTotalNum
    } else {
        return (data.value?.buyAvgPrice - formState?.takeProfitValue) * data.value?.bugTotalNum
    }
})

// 止损
const expectedLossValue = computed(() => {
    // data.tradeType  1 做多 涨了赚钱 2 做空 跌了赚钱
    if (+data.value?.tradeType === 1) {
        return (data.value?.buyAvgPrice - formState?.takeProfitValue) * data.value?.bugTotalNum
    } else {
        return (formState?.takeProfitValue - data.value?.buyAvgPrice) * data.value?.bugTotalNum
    }
})

const handleSubmit = async () => {
    if (!formState.stopLossValue && !formState.takeProfitValue) {
        return showFailToast(t('futures.please_set_tp_sl_price'))
    }

    // 做多时, 止盈价 应大于 买入价 止损价: 应小于 买入价
    // 做空时, 止盈价 应小于 买入价 止损价: 应大于 买入价
    if (+data.value.tradeType === 1) { // 做多
        if (formState.takeProfitValue && formState.takeProfitValue <= data?.value.buyAvgPrice) {
            return showFailToast(t('futures.take_profit_above_entry'))
        }
        if (formState?.stopLossValue && formState?.stopLossValue > data?.value.buyAvgPrice) {
            return showFailToast(t('futures.stop_loss_below_entry'))
        }
    } else if (+data.value?.tradeType === 2) { // 做空
        if (formState.takeProfitValue && formState.takeProfitValue > data?.value.buyAvgPrice) {
            return showFailToast(t('futures.take_profit_above_entry1'))
        }
        if (formState?.stopLossValue && formState?.stopLossValue <= data?.value.buyAvgPrice) {
            return showFailToast(t('futures.stop_loss_below_entry2'))
        }
    }

    // 验证成功: 调用止盈止损接口
    try {
        const bool = await api_post({
            url: `/position/setStopLine`,
            params: {
                positionId: data.value?.id,
                ...formState,
            },
        })
        if (bool) {
            showSuccessToast(t('_successfully'))
            show.value = false
        }
    } catch (e) {
        console.log(e)
    }
}
defineOptions({
    name: 'TackProfitStopLoss',
})
</script>
