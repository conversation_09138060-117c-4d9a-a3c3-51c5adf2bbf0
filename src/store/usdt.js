
// 个人usdt提现
export const useUsdtStore = defineStore('usdt', () => {
    const {res: withdrawalList, onRefresh} = useRequest({
        url: '/withdrawal_usdt/list',
    })

    const {res: networkTypes, onRefresh: dispatch_networkTypes} = useRequest({
        url: '/recharge/channel/getNetworkTypeList'
    })
    return {
        withdrawalList,
        onRefresh,
        dispatch_networkTypes,
        networkTypes,
    }
})

export const useNetworkTypesStore = defineStore('usdt', () => {
    const {res: networkTypes, onRefresh: dispatch_networkTypes} = useRequest({
        url: '/recharge/channel/getNetworkTypeList'
    })
    return {
        dispatch_networkTypes,
        networkTypes,
    }
})