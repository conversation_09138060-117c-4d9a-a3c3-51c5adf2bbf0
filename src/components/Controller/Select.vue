<template>
    <Controller
        v-bind="_.pick(attrs, [ 'class', 'required', 'disabled' ])"
    >
        <template #default="{ width }">
            <van-popover
                v-if="popover"
                class="overflow-y-auto! scroll_container"
                :class="maxHeight"
                :style="{ minWidth: width + 'px' }"
                :show-arrow="false"
                v-bind="popoverProps"
                :actions
                trigger="manual"
                v-model:show="popup"
                @select="onSelect"
            >
                <template #default v-if="'popoverActions' in $slots">
                    <slot name="popoverActions" @close="popup = false"/>
                </template>

                <template #reference>
                    <slot name="reference" :popup @toggle="onTogglePopup">
                        <div
                            class="flex-between"
                            :class="{ 'text-text': disabled || (_.isNil(modelValue) && _.isNil(index)) }"
                            @click="onTogglePopup"
                        >
                            <InputRender/>

                            <van-icon
                                v-if="!disabled"
                                :size="20"
                                :name="popup ? 'arrow-up' : 'arrow-down'"
                            />
                        </div>
                    </slot>
                </template>
            </van-popover>

            <template v-else>
                <InputRender/>

                <van-popup
                    teleport="body"
                    position="bottom"
                    v-model:show="popup"
                >
                    <van-picker
                        :columns
                        :columns-field-names
                        @cancel="popup = false"
                        @confirm="onConfirm"
                    >
                        <template v-for="(_, name) in $slots" :key="name" #[name]="slotProps">
                            <slot :name="name" v-bind="{ ...slotProps }"/>
                        </template>

                        <template #empty>
                            <van-empty :description="$t('common.empty')"/>
                        </template>
                    </van-picker>
                </van-popup>
            </template>
        </template>

        <template v-if="!disabled && !popover" #suffix>
            <van-icon
                :size="20"
                :name="popup ? 'arrow-up' : 'arrow-down'"
                @click="onTogglePopup"
            />
        </template>
    </Controller>
</template>

<script setup>
import _ from 'lodash'
import { Icon } from 'vant'

import Controller from './Controller.vue'

const { columns, columnsFieldNames, showLabel, inputable, popover, disabled, placeholder, maxHeight } = defineProps({
    columns: Array,
    columnsFieldNames: {
        type: Object,
        default: {
            text: 'text',
            value: 'value',
        },
    },
    showLabel: Boolean,
    inputable: Boolean,
    popover: Boolean,
    popoverProps: Object,
    disabled: Boolean,
    placeholder: String,
    maxHeight: {
        type: String,
        default: 'max-h-[264px]!',
    },
})

const emits = defineEmits([ 'select' ]),
    attrs = useAttrs()

const modelValue = defineModel('modelValue'),
    indexValue = defineModel('index', {
        type: Number,
    })

const actions = computed(() => utils_format_options(columns, columnsFieldNames, (e) => {
    return {
        color: modelValue.value === e[columnsFieldNames.value] ? 'var(--primary)' : '',
    }
}))

const popup = ref(false)

const showText = computed(() => {
    if (!_.isNil(modelValue.value)) {
        const active = _.find(columns, { [columnsFieldNames.value]: modelValue.value })

        if (active) return active[columnsFieldNames.text]
    } else if (indexValue.value >= 0 && indexValue.value <= columns.length) {
        return columns[indexValue.value]?.[columnsFieldNames.text] ?? ''
    } else return placeholder ?? ''
})

const InputRender = () => inputable
    ? h(
        'div',
        {
            class: _.compact([ 'flex-1 flex-middle' ], { 'text-text': disabled }),
        },
        [
            h(
                'input',
                {
                    class: 'flex-1 bg-controller_bg',
                    type: 'number',
                    inputmode: 'numeric',
                    value: modelValue.value,
                    onInput: ({ target: { value } }) => {
                        modelValue.value = value
                    },
                },
            ),
            modelValue.value
                ? h(
                    Icon,
                    {
                        name: 'clear',
                        onClick: () => {
                            modelValue.value = ''
                        },
                    },
                )
                : null,
        ],
    )
    : h(
        'div',
        {
            class: 'flex-1 truncate',
        },
        showText.value,
    )

const onConfirm = ({ selectedIndexes, selectedOptions }) => {
    const selected = selectedOptions[0]

    const index = selectedIndexes[0]

    onSelect(selected, index)
}

const onSelect = (option, index) => {
    const label = option[columnsFieldNames.text],
        value = option[columnsFieldNames.value]

    if (modelValue.value !== undefined) modelValue.value = showLabel ? label : value
    if (indexValue.value !== undefined) indexValue.value = index

    emits('select', { option, index })

    popup.value = false
}

const onTogglePopup = () => {
    if (!disabled) popup.value = !popup.value
}

defineOptions({ name: 'C-Select' })
</script>

<style scoped>
:deep(.van-popover__wrapper) {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>

<style>
:deep(.van-popover__content) {
    max-height: 70%; /* 你想要的高度 */
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

:deep(.van-popover__action) {
    max-height: inherit; /* 继承父容器的高度 */
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.scroll_container {
    max-height: 70% !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
}

</style>
