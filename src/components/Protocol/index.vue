<template>
    <van-popup
        round
        position="bottom"
        teleport="body"
        class="flex flex-col popupEl"
        style="min-height: 50vh; max-height: 80vh;"
        v-model:show="modelValue"
    >
        <div class="text-title leading-10 flex-between px-5 font-semibold text-md">
           <div class="flex w-full justify-center">
               {{ title }}
           </div>
            <van-icon @click="modelValue = false" name="cross"/>
        </div>

        <div class="flex flex-col overflow-y-auto">
              <div
                  class="flex-1 p-4 text-paragraph"
                  v-html="htmlStr"
                  :style="+props.type === 4 && sealImageUrl ? {
                      '--seal': `url(${sealImageUrl})`
                      } : {}"
              />
          </div>
    </van-popup>
</template>

<script setup>
import { utils_numberToWords } from '@/utils/index.js'

const {$profile} = storeToRefs(useProfileStore())
const props = defineProps({
    title: String,
    content: String,
    type: Number,
    capital_a: Number,
    capital_b: Number,
    rate: Number,
    rateAmount: Number,
    start_date: String,
    end_date: String,
    sealImageUrl: String
})

const modelValue = defineModel({
    type: Boolean,
    required: true,
})

defineOptions({ name: 'C-Protocol' })

// 封装统一的替换函数
function replaceVars(template) {
    if (!template) return ''
    let str = template
    // 乙方名字
    str = str.replace(/{{party_b}}/g, $profile.value?.realName || '')
    // 乙方手机号
    str = str.replace(/{{party_mobile}}/g, $profile.value?.mobile || '')
    // 合约开始时间
    str = str.replace(/{{contract_start_date}}/g, props?.start_date)
    // 合约终止时间
    str = str.replace(/{{contract_end_date}}/g, props?.end_date)

    // 甲方原始资金 大写
    // original_a_capital_1
    str = str.replace(/{{original_a_capital_1}}/g, utils_numberToWords(props?.capital_a))
    // 甲方原始资金 小写
    // original_a_capital_2
    str = str.replace(/{{original_a_capital_2}}/g, props?.capital_a)
    // 乙方原始资金 大写
    // original_b_capital_1
    str = str.replace(/{{original_b_capital_1}}/g, utils_numberToWords(props?.capital_b))
    // 乙方原始资金 小写
    // original_b_capital_2
    str = str.replace(/{{original_b_capital_2}}/g, props?.capital_b)
    // 利率
    // interest_rate
    str = str.replace(/{{interest_rate}}/g, props?.rate || '0.00')
    // 利率利息金额
    // interest_amount
    str = str.replace(/{{interest_amount}}/g, props?.rateAmount ?? '0.00')

    if($theme.value === 'dark') {
        str = str.replace(
            /color:\s*rgb\(\s*0\s*,\s*0\s*,\s*0\s*\)/gi,
            "color: rgb(255, 255, 255)"
        )
    }

    return str
}

const htmlStr = ref('')

// 初始化
htmlStr.value = replaceVars(props.content)

// 监听 type 或 sel 改变时更新
watch(
    () => [props.type, props.content, props.sealImageUrl, props.capital_a, props.capital_b, props.rate, props.rateAmount, props.start_date, props.end_date],
    ([type, content]) => {
        // 操盘协议
        if (+type === 4) {
            htmlStr.value = replaceVars(content)
        } else {
            htmlStr.value = content
        }
    },
    { deep: true, immediate: true }
)
</script>

<style scoped>
.text-paragraph {
    position: relative;
}

.text-paragraph::after {
    content: "";
    position: absolute;
    left: 50px;
    bottom: 60px;
    width: 120px;
    height: 120px;
    background: var(--seal) no-repeat center center;
    background-size: contain;
    pointer-events: none;
    opacity: 0.7;
}
</style>