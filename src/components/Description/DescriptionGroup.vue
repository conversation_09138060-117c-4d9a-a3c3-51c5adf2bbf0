<template>
    <div
        class="c-description-group grid gap-x-2"
        :style="{ gridTemplateColumns: `repeat(${columns}, 1fr)` }"
    >
        <slot name="prefix"/>
        <slot>
            <Render/>
        </slot>
        <slot name="suffix"/>
    </div>
</template>

<script setup>
import Description from './index.vue'

const {
    items,
    dataSource,
    layout,
    fieldNames,
    labelFormat,
    valueFormat,
    labelClass,
    valueClass,
} = defineProps({
    columns: {
        type: Number,
        default: 1,
    },
    items: Array,
    dataSource: Object,
    layout: String,
    fieldNames: {
        type: Object,
        default: {
            label: 'label',
            value: 'value',
        },
    },
    labelFormat: Function,
    valueFormat: Function,
    labelClass: [ String, Array, Object ],
    valueClass: [ String, Array, Object ],
})

const slots = useSlots()

const Render = () => items?.map((e, i) => {
    const label = e[fieldNames.label]

   const _label = typeof label === 'function' ? label(e, i) : label
    let value = e[fieldNames.value]
    if (dataSource) {
        value = dataSource[value]
    }
    if (e.render) {
        value = e.render(value, e, i)
    } else if (valueFormat) {
        value = valueFormat(value, e, i)
    }

    return h(
        Description,
        {
            'data-aos': 'fade-left',
            'data-aos-anchor': '#app',
            'data-aos-delay': i * 50,
            layout: e.layout ?? layout,
            labelClass: e.labelClass ?? labelClass,
            valueClass: e.valueClass ?? valueClass,
            label: labelFormat ? labelFormat(_label) : _label,
            class: e.class,
            value,
            tip: e.tip,
        },
        {
            default: () => slots.template?.({ ...e, value }),
        },
    )
})

defineOptions({ name: 'C-Description-Group' })
</script>
