<template>
    <div
        class="c-header h-(--header-height) px-4 flex-between overflow-hidden"
        :class="[
      { 'text-title': !transparent, 'text-white': transparent },
      !transparent && !bgColor ? 'bg-bg' : ''  // 默认加 bg-bg
    ]"
        :style="{
      backgroundColor: transparent ? 'transparent' : bgColor || ''
    }"
    >
        <div class="c-header__left" data-aos="fade-left">
            <slot name="left">
                <van-icon
                    v-if="!hideBack"
                    name="arrow-left"
                    @click="!disabled && $router.back()"
                />
            </slot>
        </div>

        <div
            class="c-header__center flex-1 text-center truncate"
            data-aos="fade-left"
            data-aos-delay="50"
        >
            <slot>
                <span>{{ title }}</span>
            </slot>
        </div>

        <div class="c-header__right" data-aos="fade-left" data-aos-delay="100">
            <slot name="right" />
        </div>
    </div>
</template>

<script setup>
defineProps({
    hideBack: Boolean,
    title: String,
    transparent: <PERSON>olean,
    disabled: Boolean,
    bgColor: { type: String, default: '' }, // 新增属性
})

defineOptions({ name: 'C-Header' })
</script>
