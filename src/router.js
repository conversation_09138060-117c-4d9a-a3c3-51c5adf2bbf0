import { createRouter, createWebHashHistory } from 'vue-router'
import _ from 'lodash'

import ActionsBar from '@/layout/main/components/ActionsBar.vue'
import ActionsBar2 from '@/layout/main/components/ActionsBar2.vue'
const _t = (...args) => i18n.global.t(...args)

// 需要实名后查看的路由
const beforeAuth = async (to, from, next) => {
    const { $profile } = storeToRefs(useProfileStore())

    if ($profile.value.auth) {
        next()
    } else {
        // 未实名提示用户去实名
        try {
            await showConfirmDialog({
                title: _t('auth.tip'), confirmButtonText: _t('auth.redirect'),
            })
            next({ path: '/authentication' })
        } catch (e) {
            next(false)
        }
    }
}

const routes = [
    {
        path: '/index', name: 'index', component: () => import('@/pages/home/<USER>/index.vue'),
    },
    {
    path: '/', name: 'mainLayout', component: () => import('@/layout/main/_TEMPLATE_/index.vue'), children: [ {
            path: 'home',
            name: 'home',
            components: {
            default: () => import('@/pages/main/home/<USER>/index.vue'),
            ActionsBar: G_TEMPLATE === 'temp_GP' ? ActionsBar2 : ActionsBar,
        },
    }, {
        path: 'quotes', name: 'quotes', components: {
            default: () => import('@/pages/main/quotes/index.vue'), ActionsBar,
        }, children: [ {
            path: 'stock',
            name: 'quotes-stock',
            alias: '',
            component: () => import('@/pages/main/quotes/routes/stock/index.vue'),
        }, {
            path: 'index', name: 'quotes-index', component: () => import('@/pages/main/quotes/routes/index/index.vue'),
        }, {
            path: 'futures',
            name: 'quotes-futures',
            component: () => import('@/pages/main/quotes/routes/futures/index.vue'),
        }, {
            path: 'collect',
            name: 'quotes-collect',
            component: () => import('@/pages/main/quotes/routes/collect/index.vue'),
        } ],
    }, {
        path: 'activity', name: 'activity', components: {
            default: () => import('@/pages/main/activity/index.vue'), ActionsBar,
        },
    },
            {
                path: 'news/list', name: 'news-list', components: {
                    default: () => import('@/pages/main/activity/news.vue'), ActionsBar,
                },
            },

            {
        path: 'account', name: 'account', components: {
            default: () => import('@/pages/main/account/index.vue'), ActionsBar,
        },
    }, {
        path: 'mine', name: 'mine', component: () => import('@/pages/main/mine/_TEMPLATE_/index.vue'),
    } ],
}, {
    path: '/auth', name: 'authLayout', component: () => import('@/layout/auth/_TEMPLATE_/index.vue'), children: [ {
        path: '', name: 'login', component: () => import('@/pages/auth/login/_TEMPLATE_/index.vue'),
    }, {
        path: 'forget', name: 'forget', component: () => import('@/pages/auth/forget/_TEMPLATE_/index.vue'),
    }, {
        path: 'register', name: 'register', component: () => import('@/pages/auth/register/_TEMPLATE_/index.vue'),
    } ],
},

    /** mine */
    // 系统设置
    {
        path: '/setting', name: 'setting', component: () => import('@/pages/main/mine/routes/setting/index.vue'),
    }, // 个人资料
    {
        path: '/profile',
        name: 'profile',
        component: () => import('@/pages/main/mine/routes/setting/routes/profile/index.vue'),
    }, {
        path: '/profile/update/:type', name: 'profile-update', component: () => import(
            '@/pages/main/mine/routes/setting/routes/profile/routes/update/index.vue'
            ),
    }, // 修改头像
    {
        path: '/avatar', name: 'avatar', component: () => import(
            '@/pages/main/mine/routes/setting/routes/profile/routes/avatar/index.vue'
            ),
    },

    /** 修改密码 */
    // 修改密码
    {
        path: '/modify/:type',
        name: 'modify',
        component: () => import('@/pages/main/mine/routes/setting/routes/modify/index.vue'),
    }, // 通过短信验证码修改
    {
        path: '/modify/:type/otp', name: 'modify_by_otp', component: () => import(
            '@/pages/main/mine/routes/setting/routes/modify/routes/modify_by_otp.vue'
            ),
    }, // 通过原密码修改
    {
        path: '/modify/:type/original', name: 'modify_by_original', component: () => import(
            '@/pages/main/mine/routes/setting/routes/modify/routes/modify_by_original.vue'
            ),
    } /** 修改密码 */,

    // 关于我们
    {
        path: '/about', name: 'about', component: () => import('@/pages/main/mine/routes/about/index.vue'),
    }, {
        path: '/about/details/:id',
        name: 'about-details',
        component: () => import('@/pages/main/mine/routes/about/details.vue'),
    }, // 新手问题
    {
        path: '/q&a', name: 'q&a', component: () => import('@/pages/main/mine/routes/q&a/index.vue'),
    }, {
        path: '/transaction/detail/:positionId',
        name: 'futures-transaction-detail',
        component: () => import('@/pages/main/account/components/transactionDetail.vue'),
    }, {
        path: '/q&a/:id', name: 'q&a-details', component: () => import('@/pages/main/mine/routes/q&a/details.vue'),
    }, {
        path: '/q&a/search', name: 'q&a-search', component: () => import('@/pages/main/mine/routes/q&a/search.vue'),
    }, // 实名认证
    {
        path: '/authentication',
        name: 'authentication',
        component: () => import('@/pages/main/mine/routes/authentication/index.vue'),
    }, // 邀请返佣
    {
        path: '/invite',
        name: 'invite',
        component: () => import('@/pages/main/mine/routes/invite/temp_bull/index.vue'),
    },
    {
       path: '/subordinate',
       name: 'subordinate',
        component: () => import('@/pages/main/mine/routes/subordinate/index.vue'),
    },
    {
        path: '/rebate',
        name: 'rebate',
        component: () => import('@/pages/main/mine/routes/rebate/index.vue'),
    },
    {
        path: '/usdt-order',
        name: 'usdt-order',
        component: () => import('@/pages/main/financial/deposit/order-detail.vue'),
        props: true,
    },
    {
        path: '/usdt-address',
        name: 'usdt-address',
        component: () => import('@/pages/main/financial/withdrawal/usdt.address.list.vue'),
        props: true,
    },
    {
        path: '/usdt-address/add',
        name: 'usdt-address-add',
        component: () => import('@/pages/main/financial/withdrawal/usdt.address.add.vue'),
        props: true,
    },
    {
        path: '/aliPay',
        name: 'ali-pay',
        component: () => import('@/pages/main/financial/withdrawal/aliPay.vue'),
        props: true,
    },
    // 任务中心
    {
        path: '/mission', name: 'mission', component: () => import('@/pages/main/mine/routes/mission/index.vue'),
    }, // VIP
    {
        path: '/vip', name: 'vip', component: () => import('@/pages/main/mine/routes/vip/index.vue'),
    }, // 汇率换算
    {
        path: '/exchange', name: 'exchange', component: () => import('@/pages/main/mine/routes/exchange/index.vue'),
    } /** mine */,

    /** 账户 */
    /** 合约相关 */
    // 申请合约 - 类型选择
    {
        path: '/contract/apply/type/:contractType',
        name: 'contract-apply-type',
        component: () => import('@/pages/main/account/routes/apply/type.vue'),
    }, // 申请合约
    {
        path: '/contract/apply/:contractType/:type',
        name: 'contract-apply',
        component: () => import('@/pages/main/account/routes/apply/index.vue'),
    }, // 扩大保证金
    {
        path: '/contract/expand/:id',
        name: 'contract-expand',
        component: () => import('@/pages/main/account/routes/apply/index.vue'),
        props: {
            operation: 'expand',
        },
    }, // 追加合约
    {
        path: '/contract/replenish/:id',
        name: 'contract-replenish',
        component: () => import('@/pages/main/account/routes/apply/index.vue'),
        props: {
            operation: 'replenish',
        },
    }, // 合约续费
    {
        path: '/contract/renewal/:id',
        name: 'contract-renewal',
        component: () => import('@/pages/main/account/routes/apply/index.vue'),
        props: {
            operation: 'renewal',
        },
    }, // 申请记录
    {
        path: '/contract/apply/record',
        name: 'contract-apply-record',
        component: () => import('@/pages/main/account/routes/apply/record.vue'),
    }, // 合约页面
    {
        path: '/contract/:id',
        name: 'contract',
        component: () => import('@/pages/main/account/routes/contract/index.vue'),
    }, // 合约详情
    {
        path: '/contract/details/:id',
        name: 'contract-details',
        component: () => import('@/pages/main/account/routes/contract/details.vue'),
    }, // 合约历史
    {
        path: '/contract/history',
        name: 'contract-history',
        component: () => import('@/pages/main/account/routes/contract/history.vue'),
    }, // 合约提盈
    {
        path: '/contract/withdrawal/:id',
        name: 'contract-withdrawal',
        component: () => import('@/pages/main/account/routes/contract/withdrawal.vue'),
    }, // 终止合约
    {
        path: '/contract/finish/:id',
        name: 'contract-finish',
        component: () => import('@/pages/main/account/routes/contract/finish.vue'),
    } /** 合约相关 */,

    /** 现货、合约通用 */
    // 历史订单
    {
        path: '/:type/order/:id?/:dataType?',
        name: 'account-order',
        component: () => import('@/pages/main/account/routes/order/index.vue'),
    }, // 资金记录
    {
        path: '/:type/financial/:id?',
        name: 'account-financial',
        component: () => import('@/pages/main/account/routes/financial/index.vue'),
    },
    // 利息券明细 interest/page
    {
        path: '/interest/list',
        name: 'interest-list',
        component: () => import('@/pages/main/account/routes/interest/index.vue'),
    },

    // 委托详情
    {
        path: '/entrust/:id',
        name: 'entrust',
        component: () => import('@/pages/main/account/routes/entrust/details.vue'),
    },

    /** 提现 */
    // 提现渠道
    {
        path: '/withdrawal/:tab?',
        name: 'withdrawal',
        component: () => import('@/pages/main/financial/withdrawal/index.vue'),
        beforeEnter: async (to, from, next) => {
            const withdrawalStore = useWithdrawalStore(), { dispatch_getWithdrawalConfig } = withdrawalStore, { $withdrawalConfig } = storeToRefs(withdrawalStore)

            try {
                await dispatch_getWithdrawalConfig()
                // 提现开关判断
                if (!$withdrawalConfig.value.withdrawalStatus) {
                    showFailToast(_t('financial.withdrawal.disabled'))
                    return next(false)
                }
                await beforeAuth(to, from, next)
            } catch (e) {
                next(-1)
            }
        },
    }, // 提现记录
    {
        path: '/withdrawal/record',
        name: 'withdrawal-record',
        component: () => import('@/pages/main/financial/withdrawal/record.vue'),
    }, // 钱包管理
    {
        path: '/wallet', name: 'wallet', component: () => import('@/pages/main/financial/wallet/index.vue'),
    }, // 绑定钱包地址
    {
        path: '/wallet/add', name: 'wallet-add', component: () => import('@/pages/main/financial/wallet/add.vue'),
    } /** 提现 */,

    /** 充值 */
    // 充值渠道
    {
        path: '/deposit/:tab?', name: 'deposit', component: () => import('@/pages/main/financial/deposit/index.vue'), // beforeEnter: beforeAuth,
    }, // 银行卡充值
    {
        path: '/deposit/bank',
        name: 'deposit-bank',
        component: () => import('@/pages/main/financial/deposit/bank.vue'),
        beforeEnter: beforeAuth,
    }, // 充值记录
    {
        path: '/deposit/record',
        name: 'deposit-record',
        component: () => import('@/pages/main/financial/deposit/record.vue'),
    } /** 充值 */,

    /** 绑定银行卡 */
    // 绑定银行卡
    {
        path: '/bank/add', name: 'bank-add', component: () => import('@/pages/main/financial/bank/add.vue'), // beforeEnter: beforeAuth,
    } /** 绑定银行卡 */,

    /** 消息中心 */
    // 消息中心
    {
        path: '/message', name: 'message', component: () => import('@/pages/main/message/index.vue'),
    } /** 消息中心 */,

    /** 股票相关 */
    // 股指开奖结果
    {
        path: '/index/results',
        name: 'index-results',
        component: () => import('@/pages/main/quotes/routes/index/routes/results.vue'),
    }, // 交易详情
    {
        path: '/index/txnInfo',
        name: 'txnInfo',
        component: () => import('@/pages/main/quotes/routes/index/routes/tnxInfo.vue'),
    }, // 行业板块 - 所有
    {
        path: '/industry', name: 'industry', component: () => import('@/pages/main/stock/routes/industry.vue'),
    }, // 行业板块 - 指定板块
    {
        path: '/industry/type',
        name: 'industry-type',
        component: () => import('@/pages/main/stock/routes/industry_type.vue'),
    },

    // 个股
    {
        path: '/stock', name: 'stock', component: () => import('@/pages/main/stock/index.vue'), children: [ {
            path: ':type/transaction',
            name: 'stock-transaction',
            component: () => import('@/pages/main/stock/routes/transaction/index.vue'),
            beforeEnter: (to, from, next) => {
                if (+to.params.type === 2) {
                    return next('/quotes/index')
                }
                next()
            },
        }, {
            path: ':type/details',
            name: 'stock-details',
            component: () => import('@/pages/main/stock/routes/details/index.vue'),
        } ],
    },

    // 期货
    {
        path: '/futures', name: 'futures', component: () => import('@/pages/main/futures/index.vue'), children: [ {
            path: 'transaction',
            name: 'futures-transaction',
            component: () => import('@/pages/main/futures/routes/transaction/index.vue'), // beforeEnter: (to, from, next) => {
            //     if (+to.params.type === 2) {
            //         return next('/quotes/index')
            //     }
            //     next()
            // },
        }, {
            path: 'details',
            name: 'futures-details',
            component: () => import('@/pages/main/futures/routes/details/index.vue'),
        } ],
    },

    // 股票搜索
    {
        path: '/stock/search/:globalSearch?',
        name: 'stock-search',
        component: () => import('@/pages/main/stock/routes/search.vue'),
    }, // 股票预警
    {
        path: '/stock/warning',
        name: 'stock-warning',
        component: () => import('@/pages/main/stock/routes/warning/index.vue'),
    }, // 添加股票预警
    {
        path: '/stock/warning/add',
        name: 'stock-warning-add',
        component: () => import('@/pages/main/stock/routes/warning/add.vue'),
    }, // 大盘
    {
        path: '/plate', name: 'plate', component: () => import('@/pages/main/stock/routes/plate.vue'),
    }, // 股票新闻
    {
        path: '/stock/news/:id',
        name: 'stock-news',
        component: () => import('@/pages/main/stock/routes/stock_news.vue'),
    } /** 股票相关 */,

    // 活动详情
    {
        path: '/activity/details',
        name: 'activity-details',
        component: () => import('@/pages/main/activity/details.vue'),
    },

    // 新闻
    {
        path: '/news', name: 'news', component: () => import('@/pages/main/news/index.vue'),
    },

    // 在线客服
    {
        path: '/service/:pImAccount', name: 'service', component: () => import('@/pages/service/index.vue'),
    },

    // AI 分析
    {
        path: '/ai', name: 'ai', component: () => import('@/pages/main/ai/_TEMPLATE_/index.vue'),
    },
    // {
    //     path: '/:pathMatch(.*)*', redirect: () => {
    //         return '/home'
    //     },
    // },
]

const router = createRouter({
    routes, history: createWebHashHistory(),
})

// 无需登录查看的页面
const unLoginRoutes = [ // 主要五个页面
    'home', 'activity', 'quotes', 'mine', // 未登录
    'authLayout', // 问题反馈
    'q&a', // 关于我们
    'about', 'about-details', // 行业板块
    'industry', 'industry-type', // 个股
    'stock-details', 'stock-search', // 新闻详情
    'news', // 大盘
    'plate', 'index' ]


router.beforeEach(async (to, from, next) => {
    const { matched, name, query, path } = to,
    { $isLogin } = storeToRefs(useProfileStore()),
        { $sysConfig, $sysConfigLoading } = storeToRefs(useSysConfigStore())

    const isNotFirst = localStorage.getItem('firstFlag')
    if (isNotFirst !== '1') {
        // if (to.path === '/auth/register') {
        //     return next()
        // }
        if ($sysConfigLoading.value) {
            // Wait for the first API response
            await new Promise((resolve) => {
                const unwatch = watch($sysConfigLoading, (loading) => {
                    if (!loading) {
                        unwatch()
                        resolve()
                    }
                }, { immediate: true })
            })
        }

        const isAntiDetectionEnabled = $sysConfig.value?.isAntiDetectionEnabled
        console.warn(isAntiDetectionEnabled, '是否开启防检查')

        if ( name !== 'index' && isAntiDetectionEnabled) {
            return next({ name: 'index', query: query?.inviteCode ? { inviteCode: query.inviteCode, path: to.path } : {} })
        }
    }

    if (path === '/') {
        return next('/home')
    }

    if (_.some(matched, e => unLoginRoutes.includes(e.name)) || unLoginRoutes.includes(name)) {
        return next()
    }

    if ($isLogin.value) return next()

    try {
        await showConfirmDialog({
            title: _t('auth.login_tip'),
            confirmButtonText: _t('auth.login_redirect'),
        })
        return next({ name: 'login' })
    } catch (e) {
        return next(false)
    }
})

export default router
