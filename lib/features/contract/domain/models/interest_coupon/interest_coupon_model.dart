import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/interest_coupon_model.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/interest_coupon_model.g.dart';

/// Interest coupon status enum
enum InterestCouponStatus {
  unused, // 未使用 (status = 1)
  partiallyUsed, // 部分使用 (status = 2)
  completelyUsed, // 已用完 (status = 3)
  expired, // 已过期 (status = 4)
}

extension InterestCouponStatusExtension on InterestCouponStatus {
  String get displayName {
    switch (this) {
      case InterestCouponStatus.unused:
        return 'unused';
      case InterestCouponStatus.partiallyUsed:
        return 'partiallyUsed';
      case InterestCouponStatus.completelyUsed:
        return 'completelyUsed';
      case InterestCouponStatus.expired:
        return 'expired';
    }
  }

  String get translationKey {
    switch (this) {
      case InterestCouponStatus.unused:
        return 'interestCouponUnused';
      case InterestCouponStatus.partiallyUsed:
        return 'interestCouponPartiallyUsed';
      case InterestCouponStatus.completelyUsed:
        return 'interestCouponCompletelyUsed';
      case InterestCouponStatus.expired:
        return 'interestCouponExpired';
    }
  }
}

@JsonSerializable()
class InterestCouponModel {
  /// Coupon ID
  int id = 0;

  /// Coupon amount
  double amount = 0.0;

  /// Currency
  String currency = '';

  /// Status: 1=unused 2=partially used 3=completely used 4=expired
  int status = 1;

  /// Remaining amount
  double remainingAmount = 0.0;

  /// Used amount
  double usedAmount = 0.0;

  /// User ID
  int userId = 0;

  /// Valid start time
  String validStartTime = '';

  /// Valid end time (NULL means permanently valid)
  String validEndTime = '';

  /// Source type: 1后台添加 2签到赠送 3活动赠送 4充值赠送 ...
  int fromType = 0;

  InterestCouponModel();

  factory InterestCouponModel.fromJson(Map<String, dynamic> json) => $InterestCouponModelFromJson(json);

  Map<String, dynamic> toJson() => $InterestCouponModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  /// Get coupon status enum
  /// Status: 1=unused 2=partially used 3=completely used 4=expired
  InterestCouponStatus get couponStatus {
    switch (status) {
      case 1:
        return InterestCouponStatus.unused;
      case 2:
        return InterestCouponStatus.partiallyUsed;
      case 3:
        return InterestCouponStatus.completelyUsed;
      case 4:
        return InterestCouponStatus.expired;
      default:
        return InterestCouponStatus.unused;
    }
  }

  /// Check if coupon is expired based on current date
  bool get isExpired {
    if (validEndTime.isEmpty) return false;
    try {
      final expiryDate = DateTime.parse(validEndTime);
      return DateTime.now().isAfter(expiryDate);
    } catch (e) {
      return false;
    }
  }

  /// Get formatted validity period string
  String get validityPeriod {
    if (validStartTime.isEmpty || validEndTime.isEmpty) return '';
    try {
      final fromDate = DateTime.parse(validStartTime);
      final toDate = DateTime.parse(validEndTime);
      return '${fromDate.year}年${fromDate.month}月${fromDate.day}日-${toDate.year}年${toDate.month}月${toDate.day}日';
    } catch (e) {
      return '$validStartTime-$validEndTime';
    }
  }
}

@JsonSerializable()
class InterestCouponListModel {
  int current = 0;
  bool hasNext = false;
  List<InterestCouponModel> records = [];
  int total = 0;

  InterestCouponListModel();

  factory InterestCouponListModel.fromJson(Map<String, dynamic> json) => $InterestCouponListModelFromJson(json);

  Map<String, dynamic> toJson() => $InterestCouponListModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  /// Get unused coupons
  List<InterestCouponModel> get unusedCoupons {
    return records.where((coupon) => coupon.couponStatus == InterestCouponStatus.unused).toList();
  }

  /// Get used and expired coupons
  List<InterestCouponModel> get usedAndExpiredCoupons {
    return records
        .where((coupon) =>
            coupon.couponStatus == InterestCouponStatus.partiallyUsed ||
            coupon.couponStatus == InterestCouponStatus.completelyUsed ||
            coupon.couponStatus == InterestCouponStatus.expired)
        .toList();
  }
}
