part of 't_trade_profile_scroll_cubit.dart';

class FTradeProfileScrollState extends Equatable {
  final DataStatus dataStatus;
  final FTradeInfoModel? tradeInfo;
  final FTradeConfigModel? tradeConfig;

  const FTradeProfileScrollState({
    this.dataStatus = DataStatus.idle,
    this.tradeInfo,
    this.tradeConfig,
  });

  FTradeProfileScrollState copyWith({
    required DataStatus status,
    FTradeInfoModel? tradeInfo,
    FTradeConfigModel? tradeConfig,
  }) {
    return FTradeProfileScrollState(
      dataStatus: status,
      tradeInfo: tradeInfo,
      tradeConfig: tradeConfig,
    );
  }

  List<({String label, String value, Color? valueColor})> makeDisplayList() {
    if (dataStatus != DataStatus.success) {
      return _makeDisplayList(
        values: List.filled(11, ''),
      );
    }
    if (dataStatus == DataStatus.success && (tradeInfo == null || tradeConfig == null)) {
      return _makeDisplayList(
        values: List.filled(11, '--'),
      );
    }
    String temp = '--';
    if (tradeConfig!.maxIncrease != 0 && tradeConfig!.minIncrease != 0) {
      temp = '${'bullish'.tr()}${tradeConfig!.maxIncrease}/${'bearish'.tr()}${tradeConfig!.minIncrease}';
    }
    if (tradeConfig!.maxIncrease != 0 && tradeConfig!.minIncrease == 0) {
      temp = '${'bullish'.tr()}${tradeConfig!.maxIncrease}';
    }
    if (tradeConfig!.maxIncrease == 0 && tradeConfig!.minIncrease != 0) {
      temp = '${'bearish'.tr()}${tradeConfig!.minIncrease}';
    }
    String temp2 = '--';
    int multiple = int.tryParse(tradeConfig!.multipleList.first) ?? tradeConfig!.multiple;
    if (multiple != 0 && tradeConfig!.marginRatio != 0) {
      temp2 = (tradeInfo!.latestPrice * multiple * tradeConfig!.marginRatio * 0.01).toStringAsFixed(2);
    }

    return _makeDisplayList(
      values: [
        /**品种       */ tradeInfo!.name,
        /**交易所     */ CNFuturesMarketType.nameFromMarket(tradeInfo!.market),
        /**杠杆倍数   */ tradeConfig!.multiple.toString(),
        /**保证金比例 */ '${tradeConfig!.marginRatio}%',
        /**最低交易   */ '${tradeConfig!.minTradeQuantity}${'lotForStockIndex'.tr()}',
        /**最高交易   */ '${tradeConfig!.maxTradeQuantity}${'lotForStockIndex'.tr()}',
        /**涨跌幅限制 */ temp,
        /**最新价     */ tradeInfo!.latestPrice.toStringAsFixed(3),
        /**杠杆倍数   */ multiple.toString(),
        /**保证金比例 */ '${tradeConfig!.marginRatio}%',
        /**1手保证金  */ temp2,
      ],
    );
  }

  List<({String label, String value, Color? valueColor})> _makeDisplayList({
    required List<String> values,
    String defaultValue = '',
  }) {
    final keys = [
      'variety',
      'exchange_f',
      'leverage_f',
      'margin_ratio',
      'minimum_trade',
      'maximum_trade',
      'price_limit',
      'latest_price',
      'leverage_f',
      'margin_ratio',
      'margin_per_lot',
    ];

    return List.generate(keys.length, (index) {
      final key = keys[index];
      final value = (index < values.length) ? values[index] : defaultValue;
      final color = (key == 'margin_per_lot') ? Color(0xffC92C31) : null;

      return (
        label: key.tr(),
        value: value,
        valueColor: color,
      );
    });
  }

  @override
  List<Object?> get props => [dataStatus, tradeInfo, tradeConfig];
}
