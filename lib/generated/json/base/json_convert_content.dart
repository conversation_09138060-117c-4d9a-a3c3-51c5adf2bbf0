// ignore_for_file: non_constant_identifier_names
// ignore_for_file: camel_case_types
// ignore_for_file: prefer_single_quotes

// This file is automatically generated. DO NOT EDIT, all your changes would be lost.
import 'package:flutter/material.dart' show debugPrint;
import 'package:gp_stock_app/core/models/entities/account.dart';
import 'package:gp_stock_app/core/models/entities/chat/chat_config_entity.dart';
import 'package:gp_stock_app/core/models/entities/contract/apply_normal_contract_config_entity.dart';
import 'package:gp_stock_app/core/models/entities/contract/apply_trial_contract_config_entity.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/core/models/entities/deposit/usdt_channel.dart';
import 'package:gp_stock_app/core/models/entities/fund_account/fund_pay_way.dart';
import 'package:gp_stock_app/core/models/entities/invite/invite_info_entity.dart';
import 'package:gp_stock_app/core/models/entities/task/task_center_response_entity.dart';
import 'package:gp_stock_app/core/models/entities/trade/trade_handling_fee_config_entity.dart';
import 'package:gp_stock_app/core/models/entities/user.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_wallet.dart';
import 'package:gp_stock_app/core/models/entities/watchlist/watchlist_item_entity.dart';
import 'package:gp_stock_app/core/models/entities/withdraw_channel_entity.dart';
import 'package:gp_stock_app/features/contract/domain/models/interest_coupon/interest_coupon_model.dart';
import 'package:gp_stock_app/features/contract/domain/models/interest_records/interest_records_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_config_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_state_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_depth_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_info_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_k_line_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_tick_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/features/profile/domain/models/third_party_channel_entity_list_entity.dart';
import 'package:gp_stock_app/shared/models/sys_settings_model/sys_config_model.dart';

JsonConvert jsonConvert = JsonConvert();

typedef JsonConvertFunction<T> = T Function(Map<String, dynamic> json);
typedef EnumConvertFunction<T> = T Function(String value);
typedef ConvertExceptionHandler = void Function(Object error, StackTrace stackTrace);
extension MapSafeExt<K, V> on Map<K, V> {
  T? getOrNull<T>(K? key) {
    if (!containsKey(key) || key == null) {
      return null;
    } else {
      return this[key] as T?;
    }
  }
}

class JsonConvert {
  static ConvertExceptionHandler? onError;
  JsonConvertClassCollection convertFuncMap = JsonConvertClassCollection();

  /// When you are in the development, to generate a new model class, hot-reload doesn't find new generation model class, you can build on MaterialApp method called jsonConvert. ReassembleConvertFuncMap (); This method only works in a development environment
  /// https://flutter.cn/docs/development/tools/hot-reload
  /// class MyApp extends StatelessWidget {
  ///    const MyApp({Key? key})
  ///        : super(key: key);
  ///
  ///    @override
  ///    Widget build(BuildContext context) {
  ///      jsonConvert.reassembleConvertFuncMap();
  ///      return MaterialApp();
  ///    }
  /// }
  void reassembleConvertFuncMap() {
    bool isReleaseMode = const bool.fromEnvironment('dart.vm.product');
    if (!isReleaseMode) {
      convertFuncMap = JsonConvertClassCollection();
    }
  }

  T? convert<T>(dynamic value, {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    if (value is T) {
      return value;
    }
    try {
      return _asT<T>(value, enumConvert: enumConvert);
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return null;
    }
  }

  List<T?>? convertList<T>(List<dynamic>? value,
      {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return value.map((dynamic e) => _asT<T>(e, enumConvert: enumConvert))
          .toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  List<T>? convertListNotNull<T>(dynamic value,
      {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return (value as List<dynamic>).map((dynamic e) =>
      _asT<T>(e, enumConvert: enumConvert)!).toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  T? _asT<T extends Object?>(dynamic value,
      {EnumConvertFunction? enumConvert}) {
    final String type = T.toString();
    final String valueS = value.toString();
    if (enumConvert != null) {
      return enumConvert(valueS) as T;
    } else if (type == "String") {
      return valueS as T;
    } else if (type == "int") {
      final int? intValue = int.tryParse(valueS);
      if (intValue == null) {
        return double.tryParse(valueS)?.toInt() as T?;
      } else {
        return intValue as T;
      }
    } else if (type == "double") {
      return double.parse(valueS) as T;
    } else if (type == "DateTime") {
      return DateTime.parse(valueS) as T;
    } else if (type == "bool") {
      if (valueS == '0' || valueS == '1') {
        return (valueS == '1') as T;
      }
      return (valueS == 'true') as T;
    } else if (type == "Map" || type.startsWith("Map<")) {
      return value as T;
    } else {
      if (convertFuncMap.containsKey(type)) {
        if (value == null) {
          return null;
        }
        var covertFunc = convertFuncMap[type]!;
        if (covertFunc is Map<String, dynamic>) {
          return covertFunc(value as Map<String, dynamic>) as T;
        } else {
          return covertFunc(Map<String, dynamic>.from(value)) as T;
        }
      } else {
        throw UnimplementedError(
            '$type unimplemented,you can try running the app again');
      }
    }
  }

  //list is returned by type
  static M? _getListChildType<M>(List<Map<String, dynamic>> data) {
    if (<AccountInfo>[] is M) {
      return data.map<AccountInfo>((Map<String, dynamic> e) =>
          AccountInfo.fromJson(e)).toList() as M;
    }
    if (<PositionEntity>[] is M) {
      return data.map<PositionEntity>((Map<String, dynamic> e) =>
          PositionEntity.fromJson(e)).toList() as M;
    }
    if (<ChatConfigEntity>[] is M) {
      return data.map<ChatConfigEntity>((Map<String, dynamic> e) =>
          ChatConfigEntity.fromJson(e)).toList() as M;
    }
    if (<ChatServiceAccountConfig>[] is M) {
      return data.map<ChatServiceAccountConfig>((Map<String, dynamic> e) =>
          ChatServiceAccountConfig.fromJson(e)).toList() as M;
    }
    if (<ApplyNormalContractConfigEntity>[] is M) {
      return data.map<ApplyNormalContractConfigEntity>((
          Map<String, dynamic> e) =>
          ApplyNormalContractConfigEntity.fromJson(e)).toList() as M;
    }
    if (<ApplyNormalContractConfigAmountList>[] is M) {
      return data.map<ApplyNormalContractConfigAmountList>((
          Map<String, dynamic> e) =>
          ApplyNormalContractConfigAmountList.fromJson(e)).toList() as M;
    }
    if (<ApplyNormalContractConfigContractConfigMap>[] is M) {
      return data.map<ApplyNormalContractConfigContractConfigMap>((
          Map<String, dynamic> e) =>
          ApplyNormalContractConfigContractConfigMap.fromJson(e)).toList() as M;
    }
    if (<ApplyNormalContractConfigContractConfigMapConfigList>[] is M) {
      return data.map<ApplyNormalContractConfigContractConfigMapConfigList>((
          Map<String, dynamic> e) =>
          ApplyNormalContractConfigContractConfigMapConfigList.fromJson(e))
          .toList() as M;
    }
    if (<ApplyNormalContractConfigRuleMap>[] is M) {
      return data.map<ApplyNormalContractConfigRuleMap>((
          Map<String, dynamic> e) =>
          ApplyNormalContractConfigRuleMap.fromJson(e)).toList() as M;
    }
    if (<ApplyTrialContractConfigEntity>[] is M) {
      return data.map<ApplyTrialContractConfigEntity>((
          Map<String, dynamic> e) => ApplyTrialContractConfigEntity.fromJson(e))
          .toList() as M;
    }
    if (<ApplyTrialContractConfigActivityRiskMap>[] is M) {
      return data.map<ApplyTrialContractConfigActivityRiskMap>((
          Map<String, dynamic> e) =>
          ApplyTrialContractConfigActivityRiskMap.fromJson(e)).toList() as M;
    }
    if (<ContractSummaryPageEntity>[] is M) {
      return data.map<ContractSummaryPageEntity>((Map<String, dynamic> e) =>
          ContractSummaryPageEntity.fromJson(e)).toList() as M;
    }
    if (<ContractSummaryPageRecord>[] is M) {
      return data.map<ContractSummaryPageRecord>((Map<String, dynamic> e) =>
          ContractSummaryPageRecord.fromJson(e)).toList() as M;
    }
    if (<ContractMarginEntity>[] is M) {
      return data.map<ContractMarginEntity>((Map<String, dynamic> e) =>
          ContractMarginEntity.fromJson(e)).toList() as M;
    }
    if (<ContractMarginAmountList>[] is M) {
      return data.map<ContractMarginAmountList>((Map<String, dynamic> e) =>
          ContractMarginAmountList.fromJson(e)).toList() as M;
    }
    if (<ProfitWithdrawalConfigEntity>[] is M) {
      return data.map<ProfitWithdrawalConfigEntity>((Map<String, dynamic> e) =>
          ProfitWithdrawalConfigEntity.fromJson(e)).toList() as M;
    }
    if (<ContractApplyAmountEntity>[] is M) {
      return data.map<ContractApplyAmountEntity>((Map<String, dynamic> e) =>
          ContractApplyAmountEntity.fromJson(e)).toList() as M;
    }
    if (<USDTDepositChannelListEntity>[] is M) {
      return data.map<USDTDepositChannelListEntity>((Map<String, dynamic> e) =>
          USDTDepositChannelListEntity.fromJson(e)).toList() as M;
    }
    if (<USDTDepositChannel>[] is M) {
      return data.map<USDTDepositChannel>((Map<String, dynamic> e) =>
          USDTDepositChannel.fromJson(e)).toList() as M;
    }
    if (<UsdtRechargeOrder>[] is M) {
      return data.map<UsdtRechargeOrder>((Map<String, dynamic> e) =>
          UsdtRechargeOrder.fromJson(e)).toList() as M;
    }
    if (<FundPayWayListEntity>[] is M) {
      return data.map<FundPayWayListEntity>((Map<String, dynamic> e) =>
          FundPayWayListEntity.fromJson(e)).toList() as M;
    }
    if (<FundPayWay>[] is M) {
      return data.map<FundPayWay>((Map<String, dynamic> e) =>
          FundPayWay.fromJson(e)).toList() as M;
    }
    if (<InviteInfoEntity>[] is M) {
      return data.map<InviteInfoEntity>((Map<String, dynamic> e) =>
          InviteInfoEntity.fromJson(e)).toList() as M;
    }
    if (<TaskCenterResponseEntity>[] is M) {
      return data.map<TaskCenterResponseEntity>((Map<String, dynamic> e) =>
          TaskCenterResponseEntity.fromJson(e)).toList() as M;
    }
    if (<TaskEntity>[] is M) {
      return data.map<TaskEntity>((Map<String, dynamic> e) =>
          TaskEntity.fromJson(e)).toList() as M;
    }
    if (<TradeHandlingFeeConfigList>[] is M) {
      return data.map<TradeHandlingFeeConfigList>((Map<String, dynamic> e) =>
          TradeHandlingFeeConfigList.fromJson(e)).toList() as M;
    }
    if (<TradeHandlingFeeConfigEntity>[] is M) {
      return data.map<TradeHandlingFeeConfigEntity>((Map<String, dynamic> e) =>
          TradeHandlingFeeConfigEntity.fromJson(e)).toList() as M;
    }
    if (<UserModel>[] is M) {
      return data.map<UserModel>((Map<String, dynamic> e) =>
          UserModel.fromJson(e)).toList() as M;
    }
    if (<USDTWalletList>[] is M) {
      return data.map<USDTWalletList>((Map<String, dynamic> e) =>
          USDTWalletList.fromJson(e)).toList() as M;
    }
    if (<USDTWallet>[] is M) {
      return data.map<USDTWallet>((Map<String, dynamic> e) =>
          USDTWallet.fromJson(e)).toList() as M;
    }
    if (<USDTNetworkTypeList>[] is M) {
      return data.map<USDTNetworkTypeList>((Map<String, dynamic> e) =>
          USDTNetworkTypeList.fromJson(e)).toList() as M;
    }
    if (<USDTNetworkType>[] is M) {
      return data.map<USDTNetworkType>((Map<String, dynamic> e) =>
          USDTNetworkType.fromJson(e)).toList() as M;
    }
    if (<WatchlistListEntity>[] is M) {
      return data.map<WatchlistListEntity>((Map<String, dynamic> e) =>
          WatchlistListEntity.fromJson(e)).toList() as M;
    }
    if (<WatchlistItemEntity>[] is M) {
      return data.map<WatchlistItemEntity>((Map<String, dynamic> e) =>
          WatchlistItemEntity.fromJson(e)).toList() as M;
    }
    if (<WithdrawChannelListEntity>[] is M) {
      return data.map<WithdrawChannelListEntity>((Map<String, dynamic> e) =>
          WithdrawChannelListEntity.fromJson(e)).toList() as M;
    }
    if (<WithdrawChannel>[] is M) {
      return data.map<WithdrawChannel>((Map<String, dynamic> e) =>
          WithdrawChannel.fromJson(e)).toList() as M;
    }
    if (<InterestCouponModel>[] is M) {
      return data.map<InterestCouponModel>((Map<String, dynamic> e) =>
          InterestCouponModel.fromJson(e)).toList() as M;
    }
    if (<InterestCouponListModel>[] is M) {
      return data.map<InterestCouponListModel>((Map<String, dynamic> e) =>
          InterestCouponListModel.fromJson(e)).toList() as M;
    }
    if (<InterestRecordsModel>[] is M) {
      return data.map<InterestRecordsModel>((Map<String, dynamic> e) =>
          InterestRecordsModel.fromJson(e)).toList() as M;
    }
    if (<InterestRecordModel>[] is M) {
      return data.map<InterestRecordModel>((Map<String, dynamic> e) =>
          InterestRecordModel.fromJson(e)).toList() as M;
    }
    if (<FTradeAcctOrderModel>[] is M) {
      return data.map<FTradeAcctOrderModel>((Map<String, dynamic> e) =>
          FTradeAcctOrderModel.fromJson(e)).toList() as M;
    }
    if (<FTradeAcctOrderRecords>[] is M) {
      return data.map<FTradeAcctOrderRecords>((Map<String, dynamic> e) =>
          FTradeAcctOrderRecords.fromJson(e)).toList() as M;
    }
    if (<FTradeConfigModel>[] is M) {
      return data.map<FTradeConfigModel>((Map<String, dynamic> e) =>
          FTradeConfigModel.fromJson(e)).toList() as M;
    }
    if (<FTradeStateModel>[] is M) {
      return data.map<FTradeStateModel>((Map<String, dynamic> e) =>
          FTradeStateModel.fromJson(e)).toList() as M;
    }
    if (<FTradeDepthModel>[] is M) {
      return data.map<FTradeDepthModel>((Map<String, dynamic> e) =>
          FTradeDepthModel.fromJson(e)).toList() as M;
    }
    if (<FTradeDepthAsk>[] is M) {
      return data.map<FTradeDepthAsk>((Map<String, dynamic> e) =>
          FTradeDepthAsk.fromJson(e)).toList() as M;
    }
    if (<FTradeDepthBid>[] is M) {
      return data.map<FTradeDepthBid>((Map<String, dynamic> e) =>
          FTradeDepthBid.fromJson(e)).toList() as M;
    }
    if (<FTradeInfoModel>[] is M) {
      return data.map<FTradeInfoModel>((Map<String, dynamic> e) =>
          FTradeInfoModel.fromJson(e)).toList() as M;
    }
    if (<FTradeKLineModel>[] is M) {
      return data.map<FTradeKLineModel>((Map<String, dynamic> e) =>
          FTradeKLineModel.fromJson(e)).toList() as M;
    }
    if (<FTradeInfoKLineModel>[] is M) {
      return data.map<FTradeInfoKLineModel>((Map<String, dynamic> e) =>
          FTradeInfoKLineModel.fromJson(e)).toList() as M;
    }
    if (<FTradeKLineItem>[] is M) {
      return data.map<FTradeKLineItem>((Map<String, dynamic> e) =>
          FTradeKLineItem.fromJson(e)).toList() as M;
    }
    if (<FTradeTickModel>[] is M) {
      return data.map<FTradeTickModel>((Map<String, dynamic> e) =>
          FTradeTickModel.fromJson(e)).toList() as M;
    }
    if (<FTradeTickRecords>[] is M) {
      return data.map<FTradeTickRecords>((Map<String, dynamic> e) =>
          FTradeTickRecords.fromJson(e)).toList() as M;
    }
    if (<FTradeListModel>[] is M) {
      return data.map<FTradeListModel>((Map<String, dynamic> e) =>
          FTradeListModel.fromJson(e)).toList() as M;
    }
    if (<FTradeListItemModel>[] is M) {
      return data.map<FTradeListItemModel>((Map<String, dynamic> e) =>
          FTradeListItemModel.fromJson(e)).toList() as M;
    }
    if (<ThirdPartyChannelListEntity>[] is M) {
      return data.map<ThirdPartyChannelListEntity>((Map<String, dynamic> e) =>
          ThirdPartyChannelListEntity.fromJson(e)).toList() as M;
    }
    if (<ThirdPartyChannelEntity>[] is M) {
      return data.map<ThirdPartyChannelEntity>((Map<String, dynamic> e) =>
          ThirdPartyChannelEntity.fromJson(e)).toList() as M;
    }
    if (<ThirdPartyChannelPayType>[] is M) {
      return data.map<ThirdPartyChannelPayType>((Map<String, dynamic> e) =>
          ThirdPartyChannelPayType.fromJson(e)).toList() as M;
    }
    if (<SysConfigModel>[] is M) {
      return data.map<SysConfigModel>((Map<String, dynamic> e) =>
          SysConfigModel.fromJson(e)).toList() as M;
    }

    debugPrint("$M not found");

    return null;
  }

  static M? fromJsonAsT<M>(dynamic json) {
    if (json is M) {
      return json;
    }
    if (json is List) {
      return _getListChildType<M>(
          json.map((dynamic e) => e as Map<String, dynamic>).toList());
    } else {
      return jsonConvert.convert<M>(json);
    }
  }
}

class JsonConvertClassCollection {
  Map<String, JsonConvertFunction> convertFuncMap = {
    (AccountInfo).toString(): AccountInfo.fromJson,
    (PositionEntity).toString(): PositionEntity.fromJson,
    (ChatConfigEntity).toString(): ChatConfigEntity.fromJson,
    (ChatServiceAccountConfig).toString(): ChatServiceAccountConfig.fromJson,
    (ApplyNormalContractConfigEntity)
        .toString(): ApplyNormalContractConfigEntity.fromJson,
    (ApplyNormalContractConfigAmountList)
        .toString(): ApplyNormalContractConfigAmountList.fromJson,
    (ApplyNormalContractConfigContractConfigMap)
        .toString(): ApplyNormalContractConfigContractConfigMap.fromJson,
    (ApplyNormalContractConfigContractConfigMapConfigList)
        .toString(): ApplyNormalContractConfigContractConfigMapConfigList
        .fromJson,
    (ApplyNormalContractConfigRuleMap)
        .toString(): ApplyNormalContractConfigRuleMap.fromJson,
    (ApplyTrialContractConfigEntity).toString(): ApplyTrialContractConfigEntity
        .fromJson,
    (ApplyTrialContractConfigActivityRiskMap)
        .toString(): ApplyTrialContractConfigActivityRiskMap.fromJson,
    (ContractSummaryPageEntity).toString(): ContractSummaryPageEntity.fromJson,
    (ContractSummaryPageRecord).toString(): ContractSummaryPageRecord.fromJson,
    (ContractMarginEntity).toString(): ContractMarginEntity.fromJson,
    (ContractMarginAmountList).toString(): ContractMarginAmountList.fromJson,
    (ProfitWithdrawalConfigEntity).toString(): ProfitWithdrawalConfigEntity
        .fromJson,
    (ContractApplyAmountEntity).toString(): ContractApplyAmountEntity.fromJson,
    (USDTDepositChannelListEntity).toString(): USDTDepositChannelListEntity
        .fromJson,
    (USDTDepositChannel).toString(): USDTDepositChannel.fromJson,
    (UsdtRechargeOrder).toString(): UsdtRechargeOrder.fromJson,
    (FundPayWayListEntity).toString(): FundPayWayListEntity.fromJson,
    (FundPayWay).toString(): FundPayWay.fromJson,
    (InviteInfoEntity).toString(): InviteInfoEntity.fromJson,
    (TaskCenterResponseEntity).toString(): TaskCenterResponseEntity.fromJson,
    (TaskEntity).toString(): TaskEntity.fromJson,
    (TradeHandlingFeeConfigList).toString(): TradeHandlingFeeConfigList
        .fromJson,
    (TradeHandlingFeeConfigEntity).toString(): TradeHandlingFeeConfigEntity
        .fromJson,
    (UserModel).toString(): UserModel.fromJson,
    (USDTWalletList).toString(): USDTWalletList.fromJson,
    (USDTWallet).toString(): USDTWallet.fromJson,
    (USDTNetworkTypeList).toString(): USDTNetworkTypeList.fromJson,
    (USDTNetworkType).toString(): USDTNetworkType.fromJson,
    (WatchlistListEntity).toString(): WatchlistListEntity.fromJson,
    (WatchlistItemEntity).toString(): WatchlistItemEntity.fromJson,
    (WithdrawChannelListEntity).toString(): WithdrawChannelListEntity.fromJson,
    (WithdrawChannel).toString(): WithdrawChannel.fromJson,
    (InterestCouponModel).toString(): InterestCouponModel.fromJson,
    (InterestCouponListModel).toString(): InterestCouponListModel.fromJson,
    (InterestRecordsModel).toString(): InterestRecordsModel.fromJson,
    (InterestRecordModel).toString(): InterestRecordModel.fromJson,
    (FTradeAcctOrderModel).toString(): FTradeAcctOrderModel.fromJson,
    (FTradeAcctOrderRecords).toString(): FTradeAcctOrderRecords.fromJson,
    (FTradeConfigModel).toString(): FTradeConfigModel.fromJson,
    (FTradeStateModel).toString(): FTradeStateModel.fromJson,
    (FTradeDepthModel).toString(): FTradeDepthModel.fromJson,
    (FTradeDepthAsk).toString(): FTradeDepthAsk.fromJson,
    (FTradeDepthBid).toString(): FTradeDepthBid.fromJson,
    (FTradeInfoModel).toString(): FTradeInfoModel.fromJson,
    (FTradeKLineModel).toString(): FTradeKLineModel.fromJson,
    (FTradeInfoKLineModel).toString(): FTradeInfoKLineModel.fromJson,
    (FTradeKLineItem).toString(): FTradeKLineItem.fromJson,
    (FTradeTickModel).toString(): FTradeTickModel.fromJson,
    (FTradeTickRecords).toString(): FTradeTickRecords.fromJson,
    (FTradeListModel).toString(): FTradeListModel.fromJson,
    (FTradeListItemModel).toString(): FTradeListItemModel.fromJson,
    (ThirdPartyChannelListEntity).toString(): ThirdPartyChannelListEntity
        .fromJson,
    (ThirdPartyChannelEntity).toString(): ThirdPartyChannelEntity.fromJson,
    (ThirdPartyChannelPayType).toString(): ThirdPartyChannelPayType.fromJson,
    (SysConfigModel).toString(): SysConfigModel.fromJson,
  };

  bool containsKey(String type) {
    return convertFuncMap.containsKey(type);
  }

  JsonConvertFunction? operator [](String key) {
    return convertFuncMap[key];
  }
}