import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/features/contract/domain/models/interest_coupon/interest_coupon_model.dart';

class InterestCouponState extends Equatable {
  const InterestCouponState({
    this.unusedCoupons = const [],
    this.usedAndExpiredCoupons = const [],
    this.isLoadingUnused = false,
    this.isLoadingUsedExpired = false,
    this.unusedCouponsLoaded = false,
    this.usedAndExpiredCouponsLoaded = false,
  });

  final List<InterestCouponModel> unusedCoupons;
  final List<InterestCouponModel> usedAndExpiredCoupons;
  final bool isLoadingUnused;
  final bool isLoadingUsedExpired;
  final bool unusedCouponsLoaded;
  final bool usedAndExpiredCouponsLoaded;

  @override
  List<Object?> get props => [
        unusedCoupons,
        usedAndExpiredCoupons,
        isLoadingUnused,
        isLoadingUsedExpired,
        unusedCouponsLoaded,
        usedAndExpiredCouponsLoaded,
      ];

  InterestCouponState copyWith({
    List<InterestCouponModel>? unusedCoupons,
    List<InterestCouponModel>? usedAndExpiredCoupons,
    bool? isLoadingUnused,
    bool? isLoadingUsedExpired,
    bool? unusedCouponsLoaded,
    bool? usedAndExpiredCouponsLoaded,
  }) {
    return InterestCouponState(
      unusedCoupons: unusedCoupons ?? this.unusedCoupons,
      usedAndExpiredCoupons: usedAndExpiredCoupons ?? this.usedAndExpiredCoupons,
      isLoadingUnused: isLoadingUnused ?? this.isLoadingUnused,
      isLoadingUsedExpired: isLoadingUsedExpired ?? this.isLoadingUsedExpired,
      unusedCouponsLoaded: unusedCouponsLoaded ?? this.unusedCouponsLoaded,
      usedAndExpiredCouponsLoaded: usedAndExpiredCouponsLoaded ?? this.usedAndExpiredCouponsLoaded,
    );
  }
}
