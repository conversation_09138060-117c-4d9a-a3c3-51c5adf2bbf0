import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/apis/interest_coupon.dart';
import 'package:gp_stock_app/shared/logic/interest_coupon/interest_coupon_state.dart';

class InterestCouponCubit extends Cubit<InterestCouponState> {
  InterestCouponCubit() : super(const InterestCouponState());

  /// Load unused coupons (status = 1)
  Future<void> loadUnusedCoupons() async {
    if (state.isLoadingUnused) return;

    emit(state.copyWith(isLoadingUnused: true));

    try {
      final result = await InterestCouponApi.fetchInterestCouponList(
        status: 1, // unused
        pageSize: 100,
      );

      emit(state.copyWith(
        unusedCoupons: result?.records ?? [],
        isLoadingUnused: false,
        unusedCouponsLoaded: true,
      ));
    } catch (e) {
      emit(state.copyWith(
        unusedCoupons: [],
        isLoadingUnused: false,
        unusedCouponsLoaded: true,
      ));
    }
  }

  /// Load used and expired coupons (status = 5)
  Future<void> loadUsedAndExpiredCoupons() async {
    if (state.isLoadingUsedExpired) return;

    emit(state.copyWith(isLoadingUsedExpired: true));

    try {
      final result = await InterestCouponApi.fetchInterestCouponList(
        status: 5, // used/expired
        pageSize: 100,
      );

      emit(state.copyWith(
        usedAndExpiredCoupons: result?.records ?? [],
        isLoadingUsedExpired: false,
        usedAndExpiredCouponsLoaded: true,
      ));
    } catch (e) {
      emit(state.copyWith(
        usedAndExpiredCoupons: [],
        isLoadingUsedExpired: false,
        usedAndExpiredCouponsLoaded: true,
      ));
    }
  }

  void reset() {
    emit(const InterestCouponState());
  }
}
