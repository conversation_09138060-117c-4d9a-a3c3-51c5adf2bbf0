import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/features/contract/domain/models/interest_coupon/interest_coupon_model.dart';

/// Interest Coupon API service
/// Handles all API calls related to interest coupons
class InterestCouponApi {
  /// Fetch interest coupon list with pagination
  static Future<InterestCouponListModel?> fetchInterestCouponList({
    required int status,
    int? fromType,
    int pageNumber = 1,
    int pageSize = 20,
  }) async {
    final queryParameters = <String, dynamic>{
      'status': status,
      'pageNumber': pageNumber,
      'pageSize': pageSize,
    };

    if (fromType != null) {
      queryParameters['fromType'] = fromType;
    }

    final res = await Http().request<InterestCouponListModel>(
      ApiEndpoints.getInterestCouponList,
      method: HttpMethod.get,
      queryParameters: queryParameters,
    );

    return res.data;
  }
}
